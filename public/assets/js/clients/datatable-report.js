/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/*!**************************************************!*\
  !*** ./resources/js/clients/datatable-report.js ***!
  \**************************************************/


$(document).ready(function () {
  var columns = [definedColumns.stt];
  datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax("/clients/ajax/report"),
    order: [[0, "desc"]],
    columns: columns
  });
});
/******/ })()
;