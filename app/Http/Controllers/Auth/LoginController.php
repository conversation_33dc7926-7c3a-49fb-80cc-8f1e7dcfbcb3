<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    use AuthenticatesUsers;

    protected $redirectTo = '/home';

    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    protected function credentials(Request $request)
    {
        $credentials = $request->only($this->username(), 'password');

        $tenant = Tenant::query()->where('domain', $request->getHost())->first();

        if (!$tenant) {
            throw ValidationException::withMessages([
                $this->username() => 'Tenant không tồn tại.',
            ]);
        }

        session(['current_tenant_id' => $tenant->id]);

        return $credentials;
    }

    protected function authenticated(Request $request, $user)
    {
        $tenantId = session('current_tenant_id');
        $tenant = Tenant::query()->find($tenantId);

        if (!$tenant || !$user->tenants()->where('tenant_id', $tenantId)->exists()) {
            Auth::logout();
            return redirect()->route('login')->withErrors([
                'Vui lòng thử lại hoặc liên hệ với admin',
            ]);
        }

        return redirect()->intended($this->redirectTo)->with('success_msg', 'Đăng nhập thành công');
    }
}
