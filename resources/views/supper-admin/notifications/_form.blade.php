@csrf
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{{ $form_title }} thông báo</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="mb-3">
                    <label class="form-label required">Tiêu đề</label>
                    <input type="text" class="form-control" name="title" value="{{ old('title', $dataEdit->title ?? '') }}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select" required>
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}" {{ old('tenant_id', $dataEdit->tenant_id ?? '') == $tenant->id ? 'selected' : '' }}>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="type" class="form-label required">Trạng thái</label>
                    <select name="type" class="form-select" id="type">
                        @foreach(App\Enums\TypeNotificationEnum::cases() as $type)
                            <option value="{{ $type->value }}"
                                @selected(old('type', $dataEdit->type->value ?? null) == $type->value)>
                                {{ $type->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-12">
                <div class="mb-3">
                    <label class="form-label required">Nội dung</label>
                    <textarea class="form-control" name="content" id="content" rows="6" required>{{ old('content', $dataEdit->content ?? '') }}</textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer text-end">
        <a href="{{ route('supper-admin.notifications.index') }}" class="btn btn-link link-secondary">
            Hủy
        </a>
        <button type="submit" class="btn btn-primary ms-auto">
            {{ $form_title }}
        </button>
    </div>
</div>
