<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Note;
use App\Models\Order;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $orders = Order::with(['tenant'])->select('id', 'tenant_id', 'order_code', 'count', 'price_per', 'total_payment', 'order_status','created_at');

            return DataTables::of($orders)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.orders.edit', $row->id);
                    $deleteUrl = route('supper-admin.notes.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('status', function ($row) {
                    return $row->order_status->toHtml();
                })
                ->editColumn('tenant', function ($row) {
                    return '<span class="badge bg-purple">'.$row->tenant->domain.'</span>' ?? 'N/A';
                })
                ->editColumn('price', function ($row) {
                    return '<span class="badge bg-success">'.number_format($row->price_per).'đ</span>' ?? 'N/A';
                })
                ->editColumn('total_payment', function ($row) {
                    return '<span class="badge bg-pink">'.number_format($row->total_payment).'đ</span>' ?? 'N/A';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->rawColumns(['action', 'status','tenant','category', 'price','total_payment'])
                ->make(true);
        }

        return view('supper-admin.orders.index');
    }

    public function edit(string|int $id){
        $order = Order::query()->findOrFail($id);

        return view('supper-admin.orders.edit', [
            'order' => $order
        ]);
    }
}
