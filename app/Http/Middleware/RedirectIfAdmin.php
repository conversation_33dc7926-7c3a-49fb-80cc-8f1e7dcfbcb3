<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error_msg', 'Vui lòng đăng nhập để tiếp tục');
        }

        $tenant = $request->tenant;
        $user = auth()->user();
        
        $tenantUser = $user->tenants()->where('tenant_id', $tenant->id)->first();
        
        if (!$tenantUser) {
            return redirect('/home');
        }
        
        $role = $tenantUser->pivot->role;
        
        if (!in_array($role, ['admin', 'super_admin'])) {
            return redirect('/home');
        }
        
        if ($role === 'super_admin' && $tenant->id !== 1) {
            return redirect('/home');
        }
        return $next($request);
    }
}
