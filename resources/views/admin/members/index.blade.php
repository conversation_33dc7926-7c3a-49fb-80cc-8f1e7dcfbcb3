@extends('admin.layouts.app')

@section('title', 'Thành viên')

@push('css')
    @include('admin.partials.datatables.style')
@endpush
@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16 d-none d-md-block">
            Danh sách thành viên
        </h4>
        <div class="">
            <a href="javascript:void(0)" class="btn btn-success">
                <i class="ri-file-excel-2-line"></i>
                Xuất file Excel
            </a>
            <a href="{{ route('admin.members.create') }}" class="btn btn-success">
                <i class="ri-add-line"></i>
                Thêm mới
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered datatables">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>Tên đăng nhập</th>
                    <th>Email</th>
                    <th>Số điện thoại</th>
                    <th>Số dư</th>
                    <th>Trạng thái</th>
                    <th>Cấp độ</th>
                    <th>Ngày tạo</th>
                    <th></th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
    <div class="modal fade" id="userDetailModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chi Tiết Thành Viên</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="userDetailContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function() {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('admin.members.index') !!}',
                columns: [
                    { data: 'id', name: 'id' },
                    { data: 'username', name: 'username' },
                    { data: 'email', name: 'email' },
                    { data: 'phone', name: 'phone' },
                    { data: 'balance', name: 'balance' },
                    { data: 'status', name: 'status' },
                    { data: 'level', name: 'level' },
                    { data: 'created_at', name: 'created_at' },
                    { data: 'action', name: 'action', orderable: false, searchable: false }
                ],
            });
        });
        $(document).ready(function() {
            $(document).on('click', '.view-detail', function() {
                var userId = $(this).data('id');
                $('#userDetailModal').modal('show');

                $.get('/admin/members/' + userId + '/details', function(data) {
                    $('#userDetailContent').html(data);
                }).fail(function() {
                    $('#userDetailContent').html('<div class="alert alert-danger">Không thể tải dữ liệu</div>');
                });
            });
        });
    </script>
@endpush
