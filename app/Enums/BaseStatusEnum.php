<?php

namespace App\Enums;

enum BaseStatusEnum: string
{
    case PUBLISHED = 'published';
    case DRAFT = 'draft';

    public function label(): string
    {
        return match ($this) {
            self::PUBLISHED => 'Đ<PERSON> xuất bản',
            self::DRAFT => 'Nháp',
        };
    }

    public function toHtml(): string
    {
        return match ($this) {
            self::PUBLISHED => '<span class="badge bg-success">Đ<PERSON> xuất bản</span>',
            self::DRAFT => '<span class="badge bg-secondary">Nháp</span>',
        };
    }
}
