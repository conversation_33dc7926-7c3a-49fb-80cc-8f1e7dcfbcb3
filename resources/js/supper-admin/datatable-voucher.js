"use strict";

import { definedColumns, xAjax, reloadTable, components } from "../table";
$(document).ready(function () {
    var columns = [
        definedColumns.stt,
        definedColumns.code,
        definedColumns.value,
        definedColumns.type,
        definedColumns.status,
        definedColumns.start_date,
        definedColumns.end_date,
        definedColumns.tenant,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/supper-admin/vouchers') + components.btn_delete(row, '/supper-admin/vouchers');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/supper-admin/vouchers/ajax/data`),
        order: [[0, "desc"]],
        columns: columns
    });
});
