<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class ManageOrderController extends Controller
{
    public function index(){
        \Assets::addScriptsDirectly('assets/js/clients/datatable-order.min.js');
        return view('clients.orders.manage.index');
    }

    public function ajax(Request $request){
        $order = Order::query()
            ->where('user_id', auth()->user()->id)
            ->where('tenant_id', $request->tenant->id);
        $keyword = $request->query('keyword');
        return DataTables::of($order)
            ->filter(function ($query) use ($keyword) {
                if ($keyword) {
                    $query->where('code', 'like', '%' . $keyword . '%');
                }
            })
            ->make(true);
    }
}
