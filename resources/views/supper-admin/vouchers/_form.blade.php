{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{{ $form_title }} voucher</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label class="form-label required">Mã voucher</label>
                    <div class="input-group">
                        <input type="text" class="form-control" name="code" id="voucher-code"
                               value="{{ old('code', $dataEdit->code ?? '') }}"
                               placeholder="Để trống để tự động tạo mã"
                               {{ $routeType == 'edit' ? 'readonly' : '' }}>
                        @if($routeType == 'create')
                            <button type="button" class="btn btn-success" id="generate-code">
                                <i class="ri-refresh-line"></i> Tạo mã
                            </button>
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select" required>
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}" {{ old('tenant_id', $dataEdit->tenant_id ?? '') == $tenant->id ? 'selected' : '' }}>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label class="form-label required">Loại voucher</label>
                    <select name="type" class="form-select" required>
                        @foreach(App\Enums\VoucherTypeEnum::cases() as $type)
                            <option value="{{ $type->value }}"
                                @selected(old('type', $dataEdit->type->value ?? 'percent') == $type->value)>
                                {{ $type->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label class="form-label required">Giá trị</label>
                    <div class="input-group">
                        <input type="number" class="form-control" name="value"
                               value="{{ old('value', $dataEdit->value ?? '') }}"
                               step="0.01" min="0" max="999999999.99" required>
                        <span class="input-group-text" id="value-unit">%</span>
                    </div>
                    <small class="text-muted">Nhập giá trị giảm giá</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label class="form-label">Ngày bắt đầu</label>
                    <input type="text" class="form-control flatpickr-input" name="start_date"
                           value="{{ old('start_date', $dataEdit->start_date ?? '') }}"
                           placeholder="Chọn ngày bắt đầu">
                    <small class="text-muted">Để trống nếu có hiệu lực ngay</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label class="form-label">Ngày kết thúc</label>
                    <input type="text" class="form-control flatpickr-input" name="end_date"
                           value="{{ old('end_date', $dataEdit->end_date ?? '') }}"
                           placeholder="Chọn ngày kết thúc">
                    <small class="text-muted">Để trống nếu không có thời hạn</small>
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <div class="form-check">
                        <input type="hidden" name="is_active" value="0">
                        <input class="form-check-input" type="checkbox" name="is_active" value="1"
                               id="is_active" {{ old('is_active', $dataEdit->is_active ?? true) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            Kích hoạt voucher
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer text-end">
        <a href="{{ route('supper-admin.vouchers.index') }}" class="btn btn-link link-secondary">
            Hủy
        </a>
        <button type="submit" class="btn btn-primary ms-auto">
            {{ $form_title }}
        </button>
    </div>
</div>
<script>
    $(document).ready(function() {
        $('.flatpickr-input').flatpickr({
            dateFormat: "d/m/Y",
            allowInput: true,
        });

        $('#generate-code').on('click', function() {
            const code = 'VC' + Math.random().toString(36).substr(2, 8).toUpperCase();
            $('#voucher-code').val(code);
        });

        $('select[name="type"]').on('change', function() {
            const type = $(this).val();
            const unit = type === 'percent' ? '%' : 'VNĐ';
            $('#value-unit').text(unit);

            const valueInput = $('input[name="value"]');
            if (type === 'percent') {
                valueInput.attr('max', '100');
                valueInput.attr('placeholder', 'Nhập % giảm giá (0-100)');
            } else {
                valueInput.attr('max', '999999999.99');
                valueInput.attr('placeholder', 'Nhập số tiền giảm');
            }
        });

        $('select[name="type"]').trigger('change');

        $('form').on('submit', function(e) {
            const startDate = $('input[name="start_date"]').val();
            const endDate = $('input[name="end_date"]').val();

            if (startDate && endDate) {
                const start = new Date(startDate.split('/').reverse().join('-'));
                const end = new Date(endDate.split('/').reverse().join('-'));

                if (start >= end) {
                    e.preventDefault();
                    alert('Ngày kết thúc phải sau ngày bắt đầu');
                    return false;
                }
            }

            const value = parseFloat($('input[name="value"]').val());
            const type = $('select[name="type"]').val();

            if (type === 'percent' && value > 100) {
                e.preventDefault();
                alert('Giá trị phần trăm không được vượt quá 100%');
                return false;
            }
        });
    });
</script>
