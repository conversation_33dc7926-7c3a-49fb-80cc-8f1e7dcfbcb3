<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfSuperAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error_msg', 'Vui lòng đăng nhập để tiếp tục');
        }

        $tenant = $request->tenant;
        $user = auth()->user();
        
        if ($tenant->id !== 1) {
            return redirect('/home');
        }
        
        $tenantUser = $user->tenants()->where('tenant_id', 1)->first();
        
        if (!$tenantUser || $tenantUser->pivot->role !== 'super_admin') {
            return redirect('/home');
        }
        
        return $next($request);
    }
}
