<?php

namespace App\Http\Controllers\Client;

use App\Enums\TransactionTypeEnum;
use App\Models\Order;
use App\Models\Post;
use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class ReportController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/common.min.js', 'assets/js/clients/datatable-report.min.js']);
        return view('clients.reports.index');
    }

    public function filter(Request $request)
    {
        $type = $request->query('type');
        $keyword = $request->query('keyword');
        $tenantID = is_object($request->tenant) ? $request->tenant->id : $request->tenant;
        $userID = auth()->user()->id;

        if ($type === 'plus') {
            $query = Transaction::query()
                ->where('tenant_id', $tenantID)
                ->where('user_id', $userID)
                ->where('type', TransactionTypeEnum::PLUS_MONEY->value);

            return DataTables::of($query)
                ->filter(function ($query) use ($keyword) {
                    if ($keyword) {
                        $query->where('code', 'like', '%' . $keyword . '%');
                    }
                })
                ->make(true);
        } else if ($type === 'minus') {
            $query = Transaction::query()
                ->where('tenant_id', $tenantID)
                ->where('user_id', $userID)
                ->where('type', TransactionTypeEnum::MINUS_MONEY->value);

            return DataTables::of($query)
                ->filter(function ($query) use ($keyword) {
                    if ($keyword) {
                        $query->where('code', 'like', '%' . $keyword . '%');
                    }
                })
                ->make(true);

        }  else {
            $query = Transaction::query()
                ->where('tenant_id', $tenantID)
                ->where('user_id', $userID);

            return DataTables::of($query)
                ->filter(function ($query) use ($keyword) {
                    if ($keyword) {
                        $query->where('code', 'like', '%' . $keyword . '%');
                    }
                })
                ->make(true);
        }
    }

}
