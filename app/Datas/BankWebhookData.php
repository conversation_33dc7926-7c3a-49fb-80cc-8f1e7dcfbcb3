<?php

namespace App\Datas;

class BankWebhookData
{
    public int $id;


    public string $gateway;

    public string $transactionDate;

    public string $accountNumber;

    public string $transactionNumber;

    public string $content;

    public string $transferType;

    public string $description;

    public int $transferAmount;

    public string $referenceCode;


    public function __construct(
        int $id,
        string $gateway,
        string $transactionDate,
        string $accountNumber,
        string $transactionNumber,
        string $content,
        string $transferType,
        string $description,
        int $transferAmount,
        string $referenceCode
    ) {
        $this->id = $id;
        $this->gateway = $gateway;
        $this->transactionDate = $transactionDate;
        $this->accountNumber = $accountNumber;
        $this->transactionNumber = $transactionNumber;
        $this->content = $content;
        $this->transferType = $transferType;
        $this->description = $description;
        $this->transferAmount = $transferAmount;
        $this->referenceCode = $referenceCode;
    }
}
