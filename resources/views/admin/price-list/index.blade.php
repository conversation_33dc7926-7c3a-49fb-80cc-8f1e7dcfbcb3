@extends('admin.layouts.app')

@section('title', 'Bảng giá')

@section('content')

    <div class="accordion custom-accordion-border accordion-border-box accordion-success" id="accordionBordered">
        @foreach($data as $item)
            <div class="accordion-item material-shadow">
                <h2 class="accordion-header" id="{{ $item->key }}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapse-{{ $item->key }}" aria-expanded="false"
                            aria-controls="collapse-{{ $item->key }}">
                        {{ $item->name }}
                    </button>
                </h2>
                <div id="collapse-{{ $item->key }}" class="accordion-collapse collapse" aria-labelledby="{{ $item->key }}"
                     data-bs-parent="#accordionBordered">
                    <div class="accordion-body">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tên gói</th>
                                <th>Th<PERSON><PERSON> viên</th>
                                <th>Cộng tác viên</th>
                                <th><PERSON><PERSON><PERSON> lý</th>
                                <th>Nh<PERSON> phân phối</th>
                                <th>Trạng thái</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($item->packages as $package)
                                <tr>
                                    <td>{{ $package->id }}</td>
                                    <td><span class="text-plain">{{ $package->name }}</span></td>
                                    <td>
                                            <span class="badge bg-success">
                                                {{ $package->member_price }}
                                            </span>
                                    </td>
                                    <td>
                                            <span class="badge bg-primary">
                                                {{ $package->collaborator_price }}
                                            </span>
                                    </td>
                                    <td>
                                            <span class="badge bg-warning">
                                                {{ $package->agency_price }}
                                            </span>
                                    </td>
                                    <td>
                                            <span class="badge bg-purple">
                                                {{ $package->distributor_price }}
                                            </span>
                                    </td>
                                    <td>
                                        {!! $package->status->toHtml() !!}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endforeach

    </div>
@endsection
