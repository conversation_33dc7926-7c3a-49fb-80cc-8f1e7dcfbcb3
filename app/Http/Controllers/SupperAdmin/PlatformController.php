<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Platform;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PlatformController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/supper-admin/datatable-platform.min.js']);
        return view('supper-admin.platforms.index');
    }

    public function ajaxData()
    {
        $data = Platform::query()->select('id', 'name', 'key', 'created_at', 'status');
        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%')
                        ->orWhere('key', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('supper-admin.platforms.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|string|max:60',
            'key' => 'required|unique:platforms|string|max:255',
        ]);

        Platform::query()->create([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'status' => $validated['status'] ?? 'published',
            'key' => $validated['key'] ?? null,
        ]);

        return redirect()->route('supper-admin.platforms.index')->with('success', 'Tạo mới thành công nền tnarg');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string|int $id)
    {
        $platform = Platform::query()->findOrFail($id);
        return view('supper-admin.platforms.edit', [
            'dataEdit' => $platform,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string|int $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|string|max:60',
            'key' => 'required|unique:platforms,key,' . $id,
        ]);
        $platform = Platform::query()->findOrFail($id);
        $platform->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'status' => $validated['status'] ?? $platform->status,
            'key' => $validated['key'],
        ]);

        return redirect()->route('supper-admin.platforms.index')
            ->with('success', 'Cập nhật thông tin thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string|int $id)
    {
        $query = Platform::query()->findOrFail($id);
        $query->delete();

        return redirect()->route('supper-admin.platforms.index')->with('success_msg', 'Xóa nền tảng thành công!');
    }
}
