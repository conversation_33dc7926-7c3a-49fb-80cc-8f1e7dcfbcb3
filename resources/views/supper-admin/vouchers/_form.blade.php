{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{{ $form_title }} bài viết</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Mã code</label>
                    <input type="text" class="form-control" name="code" value="{{ old('code', $dataEdit->code ?? '') }}" disabled>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select" required>
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}" {{ old('tenant_id', $dataEdit->tenant_id ?? '') == $tenant->id ? 'selected' : '' }}>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Định dạng</label>
                    <select name="type" class="form-select">
                        @foreach(App\Enums\VoucherTypeEnum::cases() as $type)
                            <option value="{{ $type->value }}"
                                @selected(old('type', $dataEdit->type->value ?? null) == $type->value)>
                                {{ $type->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Giá trị</label>
                    <input type="number" class="form-control" name="value" value="{{ old('value', $dataEdit->value ?? null) }}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Ngày bắt đầu</label>
                    <input type="text" class="form-control flatpickr-input" name="start_date" value="{{ old('start_date', $dataEdit->start_date ?? null) }}" placeholder="d/m/Y">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Ngày kết thúc</label>
                    <input type="text" class="form-control flatpickr-input" name="end_date" value="{{ old('end_date', $dataEdit->end_date ?? null) }}" placeholder="d/m/Y">
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer text-end">
        <a href="{{ route('supper-admin.posts.index') }}" class="btn btn-link link-secondary">
            Hủy
        </a>
        <button type="submit" class="btn btn-primary ms-auto">
            {{ $form_title }}
        </button>
    </div>
</div>
<script>
    $(document).ready(function() {
        $('.flatpickr-input').flatpickr({
            dateFormat: "d/m/Y",
        });
    });
</script>
