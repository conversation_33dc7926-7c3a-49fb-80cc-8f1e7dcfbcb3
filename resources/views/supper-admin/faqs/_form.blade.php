{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            Thông tin câu hỏi
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="tenant_id" class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select" id="tenant_id">
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}"
                                @selected(old('tenant_id', $dataEdit->tenant_id ?? null) == $tenant->id)>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                    @error('tenant_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="question" class="form-label required">Câu hỏi</label>
                    <input type="text" class="form-control" id="question" name="question"
                           value="{{ old('question', $dataEdit->question ?? null) }}">
                    @error('question')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="answer" class="form-label required">Câu trả lời</label>
                    <textarea class="form-control" id="answer" name="answer"
                              rows="5">{{ old('answer', $dataEdit->answer ?? null) }}</textarea>
                    @error('answer')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="status-faq" class="form-label required">Trạng thái</label>
                    <select name="status" class="form-select" id="status-faq">
                        @foreach(App\Enums\BaseStatusEnum::cases() as $status)
                            <option value="{{ $status->value }}"
                                @selected(old('status', $dataEdit->status->value ?? null) == $status->value)>
                                {{ $status->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-success fw-medium">
            <i class="ri-save-line"></i>
            {{$form_title}}
        </button>
    </div>
</div>
