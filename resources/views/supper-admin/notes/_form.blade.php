{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            Thông tin ghi chú
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="tenant_id" class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select" id="tenant_id">
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}"
                                @selected(old('tenant_id', $dataEdit->tenant_id ?? null) == $tenant->id)>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                    @error('tenant_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="category_id" class="form-label required">Danh mục</label>
                    <select name="category_id" class="form-select" id="category_id">
                        <option value="">Chọn danh mục</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}"
                                @selected(old('category_id', $dataEdit->category_id ?? null) == $category->id)>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="title" class="form-label required">Tiêu đề</label>
                    <input type="text" class="form-control" id="title" name="title"
                           value="{{ old('title', $dataEdit->title ?? null) }}">
                    @error('title')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="content" class="form-label required">Nội dung</label>
                    <textarea class="form-control" id="content" name="content"
                              rows="5">{{ old('content', $dataEdit->content ?? null) }}</textarea>
                    @error('content')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="image" class="form-label">Hình ảnh</label>
                    <input type="text" class="form-control" id="image" name="image"
                           value="{{ old('image', $dataEdit->image ?? null) }}">
                    @error('image')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="link_youtube" class="form-label">Link Youtube</label>
                    <input type="text" class="form-control" id="link_youtube" name="link_youtube"
                           value="{{ old('link_youtube', $dataEdit->link_youtube ?? null) }}">
                    @error('link_youtube')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="status-note" class="form-label required">Trạng thái</label>
                    <select name="status" class="form-select" id="status-note">
                        @foreach(App\Enums\BaseStatusEnum::cases() as $status)
                            <option value="{{ $status->value }}"
                                @selected(old('status', $dataEdit->status->value ?? null) == $status->value)>
                                {{ $status->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-success fw-medium mt-3">
            <i class="ri-save-line"></i>
            {{$form_title}}
        </button>
    </div>
</div>
