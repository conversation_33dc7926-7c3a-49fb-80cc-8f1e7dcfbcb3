<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use App\Enums\BaseStatusEnum;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $notifications = Notification::query()
                ->with(['tenant'])
                ->select('id','title', 'content', 'tenant_id','created_at');

            return DataTables::of($notifications)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.notifications.edit', $row->id);
                    $deleteUrl = route('supper-admin.notifications.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('title', function ($row) {
                    return '<span class="text-plain">' . $row->title . '</span>';
                })
                ->editColumn('tenant', function ($row) {
                    return '<span class="badge bg-purple">'.$row->tenant->domain.'</span>' ?? 'N/A';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->rawColumns(['action', 'status', 'tenant', 'title'])
                ->make(true);
        }

        return view('supper-admin.notifications.index');
    }

    public function create()
    {
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        return view('supper-admin.notifications.create', compact('tenants'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tenant_id' => 'required|exists:tenants,id',
            'type' => 'required'
        ]);

        DB::beginTransaction();
        try {
            Notification::query()->create($validated);
            DB::commit();

            return $this->httpResponse()->setNextUrl(route('supper-admin.notifications.index'))->withCreatedSuccessMessage();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('Lỗi khi thêm thông báo: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi thêm mới thông báo. Vui lòng thử lại!']);
        }
    }

    public function edit(string|int $id)
    {
        $notification = Notification::query()->findOrFail($id);
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.notifications.edit', [
            'dataEdit' => $notification,
            'tenants' => $tenants,
        ]);
    }

    public function update(Request $request, string|int $id)
    {
        $notification = Notification::query()->findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tenant_id' => 'required|exists:tenants,id',
            'type' => 'required',
        ]);

        DB::beginTransaction();
        try {
            $notification->update($validated);
            DB::commit();

            return $this->httpResponse()->setNextUrl(route('supper-admin.notifications.index'))->withUpdatedSuccessMessage();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('Lỗi khi cập nhật thông báo: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi cập nhật thông báo. Vui lòng thử lại!']);
        }
    }

    public function destroy(string|int $id)
    {
        $notification = Notification::query()->findOrFail($id);

        try {
            $notification->delete();
            return $this->httpResponse()->setNextUrl(route('supper-admin.notifications.index'))->withDeletedSuccessMessage();
        } catch (\Exception $exception) {
            Log::error('Lỗi khi xóa thông báo: ' . $exception->getMessage());
            return response()->json(['message' => 'Lỗi khi xóa thông báo'], 500);
        }
    }
}
