@extends('admin.layouts.app')

@section('title', 'Danh sách thanh toán')

@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            Danh sách thanh toán
        </h4>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatables">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Mã giao dịch</th>
                        <th>Số tiền</th>
                        <th>Phương thức</th>
                        <th>Trạng thái</th>
                        <th>Người dùng</th>
                        <th>Website</th>
                        <th>Ng<PERSON>y tạo</th>
                        <th>Chi tiết</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal Chi tiết thanh toán -->
    <div class="modal fade" id="paymentDetailModal" tabindex="-1" aria-labelledby="paymentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentDetailModalLabel">Chi tiết thanh toán</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Mã giao dịch:</p>
                            <p id="payment-reference"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Số tiền:</p>
                            <p id="payment-amount"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Trạng thái:</p>
                            <p id="payment-status"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Phương thức thanh toán:</p>
                            <p id="payment-method"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Người dùng:</p>
                            <p id="payment-user"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Website:</p>
                            <p id="payment-tenant"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Ngày tạo:</p>
                            <p id="payment-created"></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="mb-1 fw-medium">Cập nhật lần cuối:</p>
                            <p id="payment-updated"></p>
                        </div>
                        <div class="col-md-12 mb-3">
                            <p class="mb-1 fw-medium">Mô tả:</p>
                            <p id="payment-description"></p>
                        </div>
                        <div class="col-md-12 mb-3">
                            <p class="mb-1 fw-medium">Dữ liệu giao dịch:</p>
                            <pre id="payment-transaction-data" class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;"></pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('supper-admin.payments.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'reference', name: 'reference'},
                    {data: 'amount', name: 'amount'},
                    {data: 'payment_method', name: 'payment_method'},
                    {data: 'status', name: 'status'},
                    {data: 'user', name: 'user'},
                    {data: 'tenant', name: 'tenant'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'details', name: 'details', orderable: false, searchable: false}
                ],
                order: [[0, 'desc']],
            });

            // Xử lý sự kiện xem chi tiết
            $(document).on('click', '.view-details', function() {
                const id = $(this).data('id');
                
                $.ajax({
                    url: `/supper-admin/payments/${id}`,
                    type: 'GET',
                    success: function(response) {
                        $('#payment-reference').text(response.reference);
                        $('#payment-amount').text(response.amount);
                        $('#payment-status').text(response.status);
                        $('#payment-method').text(response.payment_method);
                        $('#payment-description').text(response.description || 'Không có mô tả');
                        $('#payment-user').text(response.user);
                        $('#payment-tenant').text(response.tenant);
                        $('#payment-created').text(response.created_at);
                        $('#payment-updated').text(response.updated_at);
                        
                        if (response.transaction_data) {
                            $('#payment-transaction-data').text(response.transaction_data);
                            $('#payment-transaction-data').parent().show();
                        } else {
                            $('#payment-transaction-data').parent().hide();
                        }
                        
                        $('#paymentDetailModal').modal('show');
                    },
                    error: function(error) {
                        console.error('Error fetching payment details:', error);
                        alert('Không thể tải thông tin chi tiết thanh toán');
                    }
                });
            });
        });
    </script>
@endpush