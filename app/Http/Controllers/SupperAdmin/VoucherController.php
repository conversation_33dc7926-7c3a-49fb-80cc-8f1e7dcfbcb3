<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SupperAdmin\VoucherRequest;
use App\Models\Tenant;
use App\Models\Voucher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class VoucherController extends Controller
{
    public function index(){
        \Assets::addScriptsDirectly('assets/js/supper-admin/datatable-voucher.min.js');
        return view('supper-admin.vouchers.index');
    }
    public function ajaxData()
    {
        $data = Voucher::query()
            ->with('tenant')
            ->select('id', 'code','value','tenant_id','type', 'start_date', 'end_date','is_active');

        return DataTables::of($data)
            ->addColumn('tenant', function ($row) {
                return $row->tenant ? $row->tenant->domain : 'N/A';
            })
            ->editColumn('type', function ($row) {
                return $row->type->label();
            })
            ->editColumn('is_active', function ($row) {
                return $row->is_active
                    ? '<span class="badge bg-success">Hoạt động</span>'
                    : '<span class="badge bg-danger">Không hoạt động</span>';
            })
            ->editColumn('start_date', function ($row) {
                return $row->start_date ? $row->start_date->format('d/m/Y') : 'Không giới hạn';
            })
            ->editColumn('end_date', function ($row) {
                return $row->end_date ? $row->end_date->format('d/m/Y') : 'Không giới hạn';
            })
            ->editColumn('value', function ($row) {
                return $row->type->value === 'percent'
                    ? number_format($row->value, 0) . '%'
                    : number_format($row->value, 0) . ' VNĐ';
            })
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('code', 'like', '%' . request('keyword') . '%');
                }
            })
            ->rawColumns(['is_active'])
            ->make(true);
    }

    public function create()
    {
        \Assets::addScriptsDirectly([
            'assets/libs/flatpickr/flatpickr.min.js',
        ])->addStylesDirectly('assets/libs/flatpickr/flatpickr.min.css');
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        return view('supper-admin.vouchers.create',[
            'tenants' => $tenants,
        ]);
    }

    /**
     * Store a newly created voucher in storage.
     */
    public function store(VoucherRequest $request)
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $voucher = Voucher::create($validated);

            DB::commit();

            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('success_msg', 'Thêm mới voucher thành công.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()
                ->route('supper-admin.vouchers.create')
                ->with('error_msg', 'Có lỗi xảy ra: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show the form for editing the specified voucher.
     */
    public function edit(string|int $id)
    {
        \Assets::addScriptsDirectly([
            'assets/libs/flatpickr/flatpickr.min.js',
        ])->addStylesDirectly('assets/libs/flatpickr/flatpickr.min.css');

        $voucher = Voucher::query()->findOrFail($id);
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.vouchers.edit', [
            'dataEdit' => $voucher,
            'tenants' => $tenants,
        ]);
    }

    /**
     * Update the specified voucher in storage.
     */
    public function update(VoucherRequest $request, string|int $id)
    {
        $voucher = Voucher::query()->findOrFail($id);

        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $voucher->update($validated);

            DB::commit();

            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('success_msg', 'Cập nhật voucher thành công.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()
                ->route('supper-admin.vouchers.edit', $id)
                ->with('error_msg', 'Có lỗi xảy ra: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified voucher from storage.
     */
    public function destroy(string|int $id)
    {
        try {
            $voucher = Voucher::query()->findOrFail($id);
            $voucher->delete();

            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('success_msg', 'Xóa voucher thành công.');
        } catch (\Exception $e) {
            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('error_msg', 'Có lỗi xảy ra khi xóa voucher: ' . $e->getMessage());
        }
    }
}
