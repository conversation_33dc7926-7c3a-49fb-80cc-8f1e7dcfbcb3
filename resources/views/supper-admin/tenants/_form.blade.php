{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            Thông tin danh mục
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="owner_id" class="form-label required">Chủ sở hữu</label>
                    <select name="owner_id" class="form-select" id="owner_id">
                        @foreach($users as $item)
                            <option value="{{ $item->id }}">{{ $item->username }}</option>
                        @endforeach
                    </select>
                    @error('owner_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="domain" class="form-label required">Domain</label>
                    <input type="text" class="form-control" id="domain" name="domain"
                           value="{{ old('domain', $dataEdit->domain ?? null) }}">
                    @error('domain')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="data" class="form-label required">Dữ liệu</label>
                    <textarea class="form-control" id="data" name="data"
                              rows="3">{{ old('data', $dataEdit->data ?? null) }}</textarea>
                    @error('data')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <input type="hidden" name="is_primary" value="0">
                    <div class="form-check form-switch" dir="ltr">
                        <input type="checkbox"
                               class="form-check-input @error('is_primary') is-invalid @enderror"
                               id="is_primary"
                               name="is_primary"
                               value="1"
                               disabled
                            {{ old('is_primary', $dataEdit->is_primary ?? false) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_primary">Là website chính</label>
                    </div>
                    <small>Tích chọn là website chính</small>
                    @error('is_primary')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <input type="hidden" name="verified" value="0">
                    <div class="form-check form-switch" dir="ltr">
                        <input type="checkbox"
                               class="form-check-input @error('verified') is-invalid @enderror"
                               id="verified"
                               name="verified"
                               value="1"
                            {{ old('verified', $dataEdit->verified ?? false) ? 'checked' : '' }}>
                        <label class="form-check-label" for="verified">Đã xác minh</label>
                    </div>
                    <small>Website đã được xác minh</small>
                    @error('verified')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                   <button class="btn btn-primary">Kiểm tra Cloudflare</button>
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="status-category" class="form-label required">Trạng thái</label>
                    <select name="status" class="form-select" id="status-package">
                        @foreach(App\Enums\BaseStatusEnum::cases() as $status)
                            <option value="{{ $status->value }}"
                                @selected(old('status', $dataEdit->status->value ?? null) == $status->value)>
                                {{ $status->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-success fw-medium">
            <i class="ri-save-line"></i>
            {{$form_title}}
        </button>
    </div>
</div>
