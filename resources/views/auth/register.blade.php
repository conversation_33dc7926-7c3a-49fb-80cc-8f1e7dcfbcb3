@extends('layouts.guest')

@section('content')
    <div class="auth-page-wrapper pt-5">
        <div class="auth-page-content">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6 col-xl-6">
                        <div class="card mt-4">
                            <div class="card-body p-4">
                                <div class="text-center mt-2">
                                    <h5 class="text-primary">Đăng ký tài kho<PERSON>n</h5>
                                </div>
                                <div class="p-2 mt-4">
                                    <form method="POST" action="{{ route('register') }}" class="form-action-request">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="email" class="form-label required">Email</label>
                                            <input type="text" class="form-control @error('email') is-invalid @enderror"
                                                   value="{{ old('email') }}" id="email"
                                                   tabindex="1"
                                                   name="email">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                        <div class="mb-3">
                                            <label for="username" class="form-label required">Tên đăng nhập</label>
                                            <input type="text"
                                                   class="form-control @error('username') is-invalid @enderror"
                                                   value="{{ old('username') }}" id="username"
                                                   tabindex="1"
                                                   name="username">
                                            @error('username')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                        <div class="mb-3">
                                            <label for="phone" class="form-label required">Số điện thoại</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                                   value="{{ old('phone') }}" id="phone"
                                                   tabindex="1"
                                                   name="phone">
                                            @error('phone')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label required" for="password-input">Mật khẩu</label>
                                            <div class="position-relative auth-pass-inputgroup mb-3">
                                                <input type="password"
                                                       class="form-control pe-5 @error('password') is-invalid @enderror"
                                                       tabindex="2"
                                                       name="password"
                                                       data-bb-password
                                                       id="password">
                                                <span
                                                    class="input-password-toggle"
                                                    type="button" data-bb-toggle-password>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                         viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                         stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                         class="icon">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/>
                                                        <path
                                                            d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>
                                                    </svg>
                                                </span>
                                                @error('password')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label required" for="password-confirmation">Xác nhận mật
                                                khẩu</label>
                                            <div class="position-relative auth-pass-inputgroup mb-3">
                                                <input type="password"
                                                       class="form-control pe-5 @error('password_confirmation') is-invalid @enderror"
                                                       tabindex="2"
                                                       name="password_confirmation"
                                                       data-bb-password
                                                       autocomplete="new-password"
                                                       id="password-confirmation">
                                                <span
                                                    class="input-password-toggle"
                                                    type="button" data-bb-toggle-password>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                         viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                         stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                         class="icon">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/>
                                                        <path
                                                            d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <button class="btn btn-success w-100 fw-medium btn-submit"
                                                    type="submit">
                                                <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M7 12l5 5l10 -10" /><path d="M2 12l5 5m5 -5l5 -5" /></svg>
                                                Đăng ký ngay
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <p class="mb-0">
                                Bạn đã có tài khoản?
                                <a href="{{  route('login') }}"
                                   class="fw-semibold text-primary text-decoration-underline">Đăng nhập</a>
                            </p>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .input-password-toggle {
            position: absolute;
            right: 0;
            top: 0;
            cursor: pointer;
            padding: 10px 15px;
            z-index: 9;
        }

        input[data-bb-password]:valid, input[data-bb-password].is-valid {
            background-image: unset;
        }

        body[dir="rtl"] .input-password-toggle {
            right: unset;
            left: 0;
        }
    </style>
    <script>
        window.addEventListener('load', function () {
            document.querySelectorAll('[data-bb-toggle-password]').forEach(button => {
                button.addEventListener('click', () => {
                    const passwordField = button.parentElement.querySelector('[data-bb-password]');

                    if (passwordField.getAttribute('type') === 'password') {
                        passwordField.setAttribute('type', 'text');
                        button.innerHTML = `
                            <svg class="icon  svg-icon-ti-ti-eye-off"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <path d="M10.585 10.587a2 2 0 0 0 2.829 2.828" />
                              <path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87" />
                              <path d="M3 3l18 18" />
                            </svg>`;
                    } else {
                        passwordField.setAttribute('type', 'password');
                        button.innerHTML = `
                            <svg class="icon  svg-icon-ti-ti-eye"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
                              <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6" />
                            </svg>`;
                    }
                });
            });
        });
    </script>
@endsection
