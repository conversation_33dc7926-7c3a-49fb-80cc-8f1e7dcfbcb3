:root {
    --color-bg: #fff;
    --color-text: #111;
    --color-dark: #000;
    --color-light: #fff;
    --color-grey: #f7f7f7;
    --color-grey-dark: #222;
    --color-border: #eee;
    --color-shadow: rgba(0,0,0,0.03);
    --font-main: 'Quicksand', sans-serif;
    --radius: 12px;
    --shadow: 0 2px 8px var(--color-shadow);
}

body {
    font-family: var(--font-main);
    background: var(--color-bg);
    color: var(--color-text);
    margin: 0;
    padding: 0;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

.header {
    background: var(--color-light);
    color: var(--color-dark);
    padding: 20px 0;
}
.header__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.logo {
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 2px;
}
.nav a {
    color: var(--color-dark);
    text-decoration: none;
    margin-left: 24px;
    font-weight: 500;
    transition: color 0.2s;
}
.nav a:hover {
    color: #bbb;
}

.hero {
    background: var(--color-bg);
    color: #111;
    padding: 80px 0 60px 0;
    text-align: center;
}
.hero__container h1 {
    font-size: 2.5rem;
    margin-bottom: 16px;
}
.hero__container p {
    font-size: 1.2rem;
    margin-bottom: 32px;
}

.btn {
    display: inline-block;
    padding: 12px 32px;
    border-radius: 24px;
    font-size: 1rem;
    font-weight: 700;
    text-decoration: none;
    transition: background 0.2s, color 0.2s;
    border: none;
    cursor: pointer;
}
.btn--primary {
    background: #111;
    color: #fff;
}
.btn--primary:hover {
    background: #333;
}
.btn--secondary {
    background: #fff;
    color: #111;
    border: 2px solid #111;
}
.btn--secondary:hover {
    background: #111;
    color: #fff;
}

.features {
    background: var(--color-grey);
    color: #111;
    padding: 60px 0;
}
.features__container {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    justify-content: center;
}
.feature {
    background: var(--color-light);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 32px 24px;
    flex: 1 1 220px;
    min-width: 220px;
    max-width: 280px;
    box-shadow: var(--shadow);
    text-align: center;
}
.feature h2 {
    font-size: 1.3rem;
    margin-bottom: 12px;
    font-weight: 700;
}
.feature p {
    font-size: 1rem;
    color: #444;
}

.cta {
    background: var(--color-dark);
    color: var(--color-light);
    padding: 60px 0;
    text-align: center;
}
.cta__container h2 {
    font-size: 2rem;
    margin-bottom: 16px;
}
.cta__container p {
    font-size: 1.1rem;
    margin-bottom: 32px;
}

.footer {
    background: var(--color-grey-dark);
    color: var(--color-light);
    padding: 20px 0;
    text-align: center;
    font-size: 0.95rem;
}

/* Tùy chỉnh icon nếu cần */
.fa-2x {
    font-size: 2em;
}

/* Responsive */
@media (max-width: 900px) {
    .features__container {
        flex-direction: column;
        align-items: center;
    }
}
@media (max-width: 600px) {
    .header__container, .features__container {
        flex-direction: column;
        align-items: flex-start;
    }
    .nav {
        margin-top: 12px;
    }
    .nav a {
        margin-left: 0;
        margin-right: 16px;
    }
    .hero__container h1 {
        font-size: 2rem;
    }
    .cta__container h2 {
        font-size: 1.3rem;
    }
    .logo {
        font-size: 1.2rem;
    }
}
.btn-outline-light{
    color: var(--color-dark);
}
