<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class TransactionController extends Controller
{
    public function index(Request $request)
    {
        $tenantID = session('current_tenant_id');
        if ($request->ajax()) {
            $transactions = Transaction::with(['user' => function ($query) {
                $query->select('id', 'username');
            }])
                ->where('tenant_id', $tenantID)
                ->select('id', 'user_id','balance_before','balance_after','math', 'amount', 'created_at', 'status', 'description')
                ->orderBy('created_at', 'desc');

            return DataTables::of($transactions)
                ->addColumn('username', function ($transaction) {
                    return '<a href="'. route('admin.members.edit', $transaction->user->id).'" class="text-plain">' . $transaction->user->username . '</a>' ?? 'N/A';
                })
                ->editColumn('amount', function ($transaction) {
                    return '<span class="badge-sm bg-success">'.number_format($transaction->balance_before).'</span>'.
                        '<span>'.$transaction->math.'</span>'.
                        '<span class="badge-sm bg-pink">'.number_format($transaction->amount) . '</span>'.
                        '<span>=</span>'.
                        '<span class="badge-sm bg-primary">'.number_format($transaction->balance_after).'</span>';
                })
                ->editColumn('created_at', function ($transaction) {
                    return $transaction->created_at->format('d/m/Y');
                })
                ->editColumn('description', function ($transaction) {
                    return $transaction->description ?? 'Không có mô tả';
                })
                ->editColumn('status', function ($transaction) {
                    return $transaction->status->toHtml() ?? 'N/A';
                })
                ->rawColumns(['amount', 'username', 'status', 'type'])
                ->make(true);
        }
        return view('admin.transactions.index');
    }


}
