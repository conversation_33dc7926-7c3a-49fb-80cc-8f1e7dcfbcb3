<?php

namespace App\Enums;

enum OrderStatusEnum: string
{
    case PROCESSING = 'Processing';
    case COMPLETED = 'Completed';
    case CANCELLED = 'Cancelled';
    case IN_PROGRESS = 'In progress';
    case REFUNDED = 'Refunded';
    case WAITING_CANCEL = 'Waiting Cancel';
    case PENDING = 'Pending';

    case ERROR = 'Error';

    public function label(): string
    {
        return match ($this) {
            self::PROCESSING => 'Đang xử lý',
            self::COMPLETED => 'Đã xong',
            self::CANCELLED => 'Đã hủy',
            self::IN_PROGRESS => 'Đang chạy',
            self::REFUNDED => 'Hoàn tiền',
            self::WAITING_CANCEL => 'Đang chờ hủy',
            self::PENDING => 'Đang chờ',
            self::ERROR => 'Lỗi',
        };
    }

    public function toHtml():string
    {
        return match ($this) {
            self::PROCESSING => '<span class="badge bg-orange"><PERSON><PERSON> xử lý</span>',
            self::COMPLETED => '<span class="badge bg-success"><PERSON><PERSON> xong</span>',
            self::CANCELLED => '<span class="badge bg-danger">Đã hủy</span>',
            self::IN_PROGRESS => '<span class="badge bg-primary">Đang chạy</span>',
            self::REFUNDED => '<span class="badge bg-pink">Hoàn tiền</span>',
            self::WAITING_CANCEL => '<span class="badge bg-warning">Đang chờ hủy</span>',
            self::PENDING => '<span class="badge bg-warning">Đang chờ</span>',
            self::ERROR => '<span class="badge bg-danger">Lỗi</span>',
        };
    }
}
