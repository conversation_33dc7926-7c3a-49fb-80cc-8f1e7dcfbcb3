"use strict";

import { definedColumns, xAjax, reloadTable, components } from "../table";
$(document).ready(function () {
    var columns = [
        definedColumns.stt,
        definedColumns.code,
        definedColumns.username,
        definedColumns.price,
        definedColumns.type,
        definedColumns.status,
        definedColumns.created_at,
        definedColumns.tenant,
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/supper-admin/transactions/ajax/data`),
        order: [[0, "desc"]],
        columns: columns
    });
});
