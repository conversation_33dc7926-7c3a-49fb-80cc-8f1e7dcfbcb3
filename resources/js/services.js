import Swal from "sweetalert2";
var allPrices = {};
var defaultMin = 10, defaultMax = 20000, selectedServer, selectedServerInt, selectedPrice;
var currency = 'VND';
function getNumber(number) {
    if (parseFloat(number) > parseInt(number)) {
        return parseFloat(number).toFixed(1);
    } else {
        return parseInt(number);
    }
}

function humanQuantity(number) {
    if (number >= 1000000) return getNumber(number / 1000000) + 'tr';
    if (number >= 1000) return getNumber(number / 1000) + 'k';
    return number;
}

$(document).ready(function () {
    $.get(`/load/services/${platform}/${category}`)
        .done(function (data) {
            allPrices = data.prices;
            var priceStatusMap = data.status;
            Object.keys(allPrices).forEach(function (key) {

                let price = allPrices[key];

                var elm = $(`[name="package"][value="${key}"]`);
                if (!elm) return;

                var parent = elm['parent']();
                parent.closest('.radio-package').addClass(price.status);
                if (price.main_hidden) parent.closest('.radio-package').remove();
                parent.append(`<span class="package-note has-dot">: ${price.name || ''}</span>`);
                parent.append(`<span class="package-price">
                          <span class="money-value">${formatMoney(price.price)}</span>
                          <span class="currency-unit">₫</span>
                      </span>`);
                parent.append(`<span class="package-status">${priceStatusMap[price.status]}</span>`);

                var priceInfo = [
                    `<span class="package-id package-id-${key}">ID: <span>${price.id}</span></span>`
                ];
                if (price.info) priceInfo.push(ln2br(price.info));

                if (price.max) priceInfo.push(`<span class="text-max">Tối thiểu/Tối đa: ${humanQuantity(price.min)}/${humanQuantity(price.max)}</span>`);

                if (priceInfo.length) parent.closest('.radio-package').after(`<div class="package-info package-section ${key}-visible">${priceInfo.join('<br />')}</div>`);
            });

            if (!$('.radio-package input[type="radio"][name="package"]:checked').val())
                $('.radio-package:not(.paused) input[name="package"]').first().prop('checked', true);

            $('.package-section').hide();
            $('[data-sv]:not(option)').hide();
            $('input[name="package"]:checked').trigger('change');

        });

    $('.radio-package input[type="radio"][name="package"]').change(function () {
        if ($('input[name="reaction"]').length) {
            $('input[name="reaction"]').prop('checked', false);
            $('input[name="reaction"][value="like"]').prop('checked', true);
        }
        selectedServer = $(this).val();
        selectedServerInt = Number(selectedServer.replace('package-', ''));

        var hasCount = $('#count').length > 0;
        onToggleServerArea(selectedServer);

        selectedPrice = allPrices[selectedServer];

        var min = selectedPrice.min || defaultMin;
        var max = selectedPrice.max || defaultMax;

        if (hasCount) {
            $('#count').attr('min', min);
            $('#count').attr('max', max);
            $('#count').val(min);
        }

        if (selectedPrice) {
            let originPrice = selectedPrice.price;
            $('#price').attr('data-price', originPrice);

            let priceValue = selectedPrice.price;

            $('#price').val(priceValue)
                .attr('min', priceValue);

            if (hasCount) {
                $('#count').trigger('change');
            }

            if (typeof selfCalculatorPrice === "function") selfCalculatorPrice(true);

            if ($('.area-interval-order').length) {
                const allowIntervalOrder = selectedPrice.allow_interval_order;
                $('.area-interval-order')[allowIntervalOrder ? 'show' : 'hide']();
                if (!allowIntervalOrder) {
                    $('#section_interval_order').find('input').prop('required', allowIntervalOrder);
                }
            }
        }

        if (typeof selfOnChangeServer === 'function') selfOnChangeServer(selectedServer);
    });
});

function onToggleServerArea(selectedServer) {
    $('.package-section').hide();
    $('.' + selectedServer + '-visible').show();

    $('[data-sv]:not(option)').hide();
    $('option[data-sv]').prop('disabled', true);

    const sv = selectedServer.replace('package-', '');
    $('[data-sv]').each(function () {
        let packages = $(this).attr('data-sv').split(',');
        if (packages.includes(sv)) {
            if ($(this).is('option')) {
                $(this).prop('disabled', false)
            } else {
                $(this).show();
            }
        }
    });

    setTimeout(() => {
        if ($('option[data-sv]').length) {
            $('#formUserAction select').each(function () {
                var $select = $(this);
                if ($select.find(':selected').is(':disabled')) {
                    $select.val($select.find('option:not(:disabled)').first().val()).change();
                }
            });
        }
    }, 50);
}
function ln2br(text) {
    return text.replace(/(?:\r\n|\r|\n)/g, '<br>');
}
$("#count").change(function() {
  if (typeof selfCalculatorPrice === "function") return selfCalculatorPrice();

  var count = Number($(this).val());
  var singlePrice = Number($("#price").attr('data-price'));
  var total;

  if (!(count > 0)) return toastr.error("Số lượng không được bé hơn 0");

    total = count * singlePrice;

  $("#total").val(formatMoney(total, ' VND'));
  Base.showSuccess("Cập nhật giá thành công");
});


function formatMoney(input, suffix = '', roundNumber = true) {
    if (!suffix) suffix = '';
    if (suffix == 'd') suffix = '₫';

    if (currency === 'usd') {
        roundNumber = false;
        if (suffix === '₫') suffix = '$';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'USD')
    } else if (currency === 'baht') {
        roundNumber = false;
        if (suffix === '₫') suffix = '฿';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'Baht')
    }

    var number = parseFloat(input);
    if (roundNumber) number = Math.floor(number);
    if (isNaN(number)) return input;

    if (currency === 'usd') number = round(number, 6);
    if (currency === 'baht') number = round(number, 4);

    // If float
    if (number && number.toString().includes('.')) {
        let parts = number.toString().split('.');
        return formatMoney(parts[0]) + '.' + parts[1] + suffix;
    }
    else {
        return number.toLocaleString() + suffix;
    }
}
function selfOnChangeServer(server) {

    const serverMultiReaction = ['package-1', 'package-2'];

    if (Number(selectedPrice.allow_reaction)) {
        $('.fb-reaction .checkbox[data-reaction]').show();
    } else {
        $('.fb-reaction .checkbox[data-reaction]').hide();
        $('.fb-reaction .checkbox[data-reaction=like]').show();
    }

    const inputReactions = $('.fb-reaction .checkbox[data-reaction] input');
    if (serverMultiReaction.includes(server)) {
        inputReactions.prop('type', 'checkbox');
    } else {
        inputReactions.prop('type', 'radio');
    }
}
function swalError(message, title = 'Lỗi') {
    return Swal.fire({
        icon: 'error',
        title: title,
        text: message,
        confirmButtonText: 'Đóng'
    });
}
function swalSuccess(message = 'Thao tác thành công!', title = 'Thành công') {
    return Swal.fire({
        icon: 'success',
        title: title,
        text: message,
        confirmButtonText: 'Đóng'
    });
}

async function swalConfirm(message, confirmText = 'Đồng ý', cancelText = 'Huỷ') {
    const result = await Swal.fire({
        icon: 'warning',
        title: 'Xác nhận',
        text: message,
        showCancelButton: true,
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        reverseButtons: true
    });

    return result.isConfirmed;
}
function swalLoading(message = 'Vui lòng chờ...', title = 'Đang xử lý') {
    Swal.fire({
        title: title,
        text: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        allowEnterKey: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

$('#formUserAction').submit(async function (e) {
    e.preventDefault()

    if ($('input[name=package]').length && $('input[name="package"]:checked')) {
        var parent = $('input[name="server"]:checked').closest('.radio-server');
        if (parent.hasClass('pause')) return swalError('Gói này đang tạm bảo trì. Hãy chọn gói khác nhé');
        if (parent.hasClass('slow') && !await swalConfirm('Gói này đang bị chậm. Nếu cần nhanh hãy lựa chọn gói khác.', 'Tiếp tục', 'Huỷ')) return;
    }

    var url = $(this).attr("action");
    $("#submit").attr("disabled", true);
    swalLoading('Đang xử lý, vui lòng chờ');
    $.ajax({
        type: "POST",
        url: url,
        data: $(this).serialize(),
        success: function (data) {
            console.log(data)
            if (data.status === 200) {
                swalSuccess(data.message);
                $([document.documentElement, document.body]).animate({
                    scrollTop: $("#formUserAction").offset().top - 300
                }, 500);
            }
            else {
                if (data.message && data.message.toString()) {
                    let errorMsg = data.message.toString();

                    if (errorMsg.includes('nhấn "Tiếp tục mua" để thử lại')) {
                        swalConfirm(data.message, 'Tiếp tục mua').then(function (confirm) {
                            if (confirm) $("#formUserAction").trigger('submit');
                        });
                        return;
                    }
                }

                swalError(data.message);
            }
        },
        error: function (xhr) {
            if (xhr.status === 422) {
                // Laravel validation error
                const errors = xhr.responseJSON.errors;
                let messages = '';

                for (let key in errors) {
                    if (errors.hasOwnProperty(key)) {
                        messages += `${errors[key][0]}`;
                    }
                }

                swalError(messages);
            }
            if (xhr.status === 400) {
                console.log(xhr)
                swalError(xhr.responseJSON.data.message || 'Yêu cầu không hợp lệ. Vui lòng kiểm tra lại thông tin đã nhập.');
            }
            if (xhr.status === 500) {
                swalError('Đã có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại sau.');
            }
            else {
                swalError('Quá thời gian chờ khi tạo đơn, đơn hàng của bạn đang được xử lý.' +
                    ' Vui lòng đợi 1 vài phút rồi tải lại trang, nếu đơn hàng chưa được tạo thì mới mua đơn mới!');
            }
        },
        complete: function () {
            $("#submit").removeAttr("disabled");
        }
    });
})

function getContentArray(text){
    return text.trim().split("\n").filter(function (line) { return line && line.trim().length });
}

$('[name=comments]').change(function () {
    $(this).val(getContentArray($(this).val()).join("\n"));
});
$('#price').keyup(function () {
    $('#count').trigger('change');
});

$('#price').change(function () {
    $('#count').trigger('change');
});
