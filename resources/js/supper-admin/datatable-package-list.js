"use strict";

import { definedColumns, xAjax, reloadTable , components} from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.stt,
    definedColumns.name,
    definedColumns.key,
    definedColumns.status,
    definedColumns.created_at,
    definedColumns.action((data, type, row) => {
      return components.btn_edit(row, '/supper-admin/package-lists') + components.btn_delete(row, '/supper-admin/package-lists');
    }),
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/supper-admin/package-lists/ajax/data`),
    order: [[ 0, "desc" ]],
    columns: columns
  });
});
