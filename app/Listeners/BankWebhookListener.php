<?php

namespace App\Listeners;

use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Enums\TypeNotificationEnum;
use App\Events\BankWebhookEvent;
use App\Models\Notification;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class BankWebhookListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BankWebhookEvent $event): void
    {
        if (strtolower($event->bankWebhookData->transferType) !== 'in') {
            return;
        }

        $data = $event->bankWebhookData;
        $user = User::query()->where('code', $event->info)->first();

        if (!$user) {
            return;
        }

        DB::transaction(function () use ($user, $data) {
            $amount = $data->transferAmount;
            $balanceBefore = $user->balance;
            $balanceAfter = $balanceBefore + $amount;

            $user->update([
                'balance' => $balanceAfter,
                'total_deposit' => $user->total_deposit + $amount,
            ]);

            $tenantId = optional($user->tenants()->first())->id ?? config('app.default_tenant_id');

            Transaction::query()->create([
                'user_id' => $user->id,
                'tenant_id' => $tenantId,
                'admin_id' => $user->id,
                'type' => TransactionTypeEnum::DEPOSIT->value,
                'math' => '+',
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'status' => TransactionStatusEnum::COMPLETED->value,
                'description' => 'Nạp tiền qua ' . $data->gateway,
            ]);

            Notification::query()->create([
                'title' => 'Nạp tiền thành công',
                'content' => 'Nạp thành công <span class="text-orange fw-semibold">'.number_format($amount).'</span>',
                'tenant_id' => $tenantId,
                'user_id' => $user->id,
                'is_read' => false,
                'amount' => $amount,
                'type' => TypeNotificationEnum::USER->value,
            ]);
        });
    }

}
