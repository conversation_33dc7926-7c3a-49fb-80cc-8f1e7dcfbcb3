<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
class UserActivityLog extends Model
{
    protected $fillable = [
        'action',
        'user_agent',
        'reference_url',
        'reference_name',
        'ip_address',
        'user_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    protected static function booted(): void
    {
        static::creating(function ($model): void {
            $model->user_agent = $model->user_agent ?? Request::userAgent();
            $model->ip_address = $model->ip_address ?? Request::ip();

            if ($model->reference_url) {
                $homepageUrl = config('app.url');
                $model->reference_url = str_replace($homepageUrl, '', $model->reference_url);
            }

            $model->user_id = $model->user_id ?? Auth::id();
        });
    }

    public function getDescription(): string
    {
        $name = $this->reference_name;

        if ($this->reference_name && $this->reference_url) {
            $name = '<a href="' . e($this->reference_url) . '" style="color: #1d9977">' . e($this->reference_name) . '</a>';
        }

        $time = '<span class="small italic">' . $this->created_at->diffForHumans() . '</span>';

        return trans('validation.actions.' . $this->action, ['name' => $name]) . ' . ' . $time;
    }
}
