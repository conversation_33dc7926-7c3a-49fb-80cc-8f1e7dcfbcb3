    <div class="text-center">
        <div class="mb-3">
            <img src="{{ $user->avatar ?? asset('assets/images/user.png') }}"
                 class="img-thumbnail rounded-circle"
                 width="150"
                 alt="Avatar">
        </div>
        <h4>{{ $user->full_name ?? $user->username }}</h4>
        <p class="text-muted">{{ $user->email }}</p>
    </div>

        <table class="table table-bordered">
            <tr>
                <th>Tên đầy đủ</th>
                <td>{{ $user->full_name }}</td>
            </tr>
            <tr>
                <th>Tên đăng nhập</th>
                <td>{{ $user->username }}</td>
            </tr>
            <tr>
                <th>Số điện thoại</th>
                <td>{{ $user->phone ?? 'N/A' }}</td>
            </tr>
            <tr>
                <th>Sinh nhật</th>
                <td>{{ $user->dob ?? '' }}</td>
            </tr>
            <tr>
                <th>Trạng thái</th>
                <td>{!! $user->status->toHtml() !!}</td>
            </tr>
            <tr>
                <th>Cấp độ</th>
                <td>{!! $user->level->toHtml() !!}</td>
            </tr>
            <tr>
                <th>Số dư</th>
                <td><span class="badge bg-pink">{{ number_format($user->balance) }}</span></td>
            </tr>
            <tr>
                <th>Số tiền đã nạp</th>
                <td><span class="text-plain">{{ number_format($user->total_deposit) }}</span></td>
            </tr>
            <tr>
                <th>Số tiền đã tiêu</th>
                <td><span class="text-plain">{{ number_format($user->total_spent) }}</span></td>
            </tr>
            <tr>
                <th>Ngày tạo</th>
                <td>{{ $user->created_at->format('d/m/Y H:i') }}</td>
            </tr>
        </table>

@if($user->description)
    <div class="row mt-3">
        <div class="col-12">
            <h5>Thông tin bổ sung</h5>
            <div class="card">
                <div class="card-body">
                    {{ $user->description }}
                </div>
            </div>
        </div>
    </div>
@endif
