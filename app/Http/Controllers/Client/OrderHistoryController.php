<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Platform;
use App\Models\Category;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class OrderHistoryController extends Controller
{
    public function ajax(Request $request)
    {
        $query = Order::query()
            ->with(['package.packageList', 'category.platform', 'package.category'])
            ->where('user_id', auth()->user()->id)
            ->where('tenant_id', $request->tenant->id);

        if ($request->has('platform') && $request->platform) {
            $platform = Platform::query()->where('key', $request->platform)->first();
            if ($platform) {
                $query->whereHas('category.platform', function($q) use ($platform) {
                    $q->where('id', $platform->id);
                });
            }
        }

        if ($request->has('category') && $request->category) {
            $category = Category::query()->where('key', $request->category)->first();
            if ($category) {
                $query->where('category_id', $category->id);
            }
        }

        // Filter by status if not 'all'
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('order_status', $request->status);
        }

        return DataTables::of($query)
            ->filter(function ($query) use ($request) {
                if ($request->has('search') && $request->search['value']) {
                    $searchValue = $request->search['value'];
                    $query->where(function($q) use ($searchValue) {
                        $q->where('order_code', 'like', '%' . $searchValue . '%')
                          ->orWhere('url', 'like', '%' . $searchValue . '%')
                          ->orWhere('note', 'like', '%' . $searchValue . '%');
                    });
                }
            })
            ->make(true);
    }

    public function counts(Request $request)
    {
        try {
            $baseQuery = Order::query()
                ->where('user_id', auth()->user()->id)
                ->where('tenant_id', $request->tenant->id);

            if ($request->has('platform') && $request->platform) {
                $platform = Platform::query()->where('key', $request->platform)->first();
                if ($platform) {
                    $baseQuery->whereHas('category.platform', function($q) use ($platform) {
                        $q->where('id', $platform->id);
                    });
                }
            }

            if ($request->has('category') && $request->category) {
                $category = Category::query()->where('key', $request->category)->first();
                if ($category) {
                    $baseQuery->where('category_id', $category->id);
                }
            }

            $counts = [
                'all' => (clone $baseQuery)->count(),
                'Pending' => (clone $baseQuery)->where('order_status', 'Pending')->count(),
                'Processing' => (clone $baseQuery)->where('order_status', 'Processing')->count(),
                'In progress' => (clone $baseQuery)->where('order_status', 'In progress')->count(),
                'Completed' => (clone $baseQuery)->where('order_status', 'Completed')->count(),
                'Cancelled' => (clone $baseQuery)->where('order_status', 'Cancelled')->count(),
                'Refunded' => (clone $baseQuery)->where('order_status', 'Refunded')->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $counts
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading counts'
            ], 500);
        }
    }
}
