"use strict";

import { definedColumns, xAjax, reloadTable, components } from "../table";

$(document).ready(function() {
    let currentFilter = 'all';
    let currentPlatform = null;
    let currentCategory = null;
    
    // Columns for history table
    var columns = [
        definedColumns.stt,
        definedColumns.order_code,
        definedColumns.url,
        definedColumns.count,
        definedColumns.price_per,
        definedColumns.total_payment,
        definedColumns.note,
        definedColumns.admin_note,
        definedColumns.created_at,
        definedColumns.order_status,
    ];

    // Initialize DataTable
    let historyTable = $('#datatable-history').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: {
            url: '/orders/history/ajax',
            data: function(d) {
                d.status = currentFilter;
                d.platform = currentPlatform;
                d.category = currentCategory;
            }
        },
        order: [[ 0, "desc" ]],
        columns: columns
    });

    // Store table reference for reloading
    reloadTable.datatableHistory = historyTable;

    // Handle filter clicks
    $('.tab-filter-history .nav-link').on('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs
        $('.tab-filter-history .nav-link').removeClass('active');
        
        // Add active class to clicked tab
        $(this).addClass('active');
        
        // Get filter value
        currentFilter = $(this).data('filter');
        
        // Reload table with new filter
        historyTable.ajax.reload();
    });

    // Function to update platform and category from parent scope
    window.updateHistoryFilter = function(platform, category) {
        currentPlatform = platform;
        currentCategory = category;
        
        if (historyTable) {
            historyTable.ajax.reload();
            loadOrderCounts();
        }
    };

    // Function to load order counts for each status
    function loadOrderCounts() {
        if (!currentPlatform || !currentCategory) return;
        
        $.ajax({
            url: '/orders/history/counts',
            method: 'GET',
            data: {
                platform: currentPlatform,
                category: currentCategory
            },
            success: function(response) {
                if (response.success) {
                    // Update counts in tabs
                    $('.tab-filter-history .nav-link').each(function() {
                        const filter = $(this).data('filter');
                        const count = response.data[filter] || 0;
                        $(this).find('.count').text(count);
                    });
                }
            },
            error: function() {
                console.log('Error loading order counts');
            }
        });
    }

    // Load initial counts when platform and category are available
    if (typeof platform !== 'undefined' && typeof category !== 'undefined') {
        currentPlatform = platform;
        currentCategory = category;
        loadOrderCounts();
    }
});
