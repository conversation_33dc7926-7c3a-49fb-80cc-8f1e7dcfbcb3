<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        View::composer(['clients.partials.sidebar', 'admin.partials.sidebar'], function ($view) {
            $user = auth()->user();
            $tenantId = session('current_tenant_id');

            $role = $user?->tenants()
                ->where('tenant_id', $tenantId)
                ->first()
                ?->pivot
                ?->role;

            $view->with('currentRole', $role);
        });
    }
}
