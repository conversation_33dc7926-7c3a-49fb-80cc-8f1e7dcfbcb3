@extends('clients.layouts.app')

@section('title', 'Thông tin cá nhân')

@section('content')
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="widget-top">
                        <div class="widget-top__avatar">
                            <img src="{{ $user->avatar ?? asset('assets/images/user.png') }}" alt="">
                        </div>
                        <div class="widget-top__content">
                            <div class="widget-top__head">
                                <span class="fw-medium text-dark">
                                    {{ $user->full_name ?? $user->username }}
                                    <i class="ri-verified-badge-fill text-primary fs-18"></i>
                                </span>
                            </div>
                            <div class="widget-top__subhead">
                                <span class="fw-medium text-muted">
                                    <i class="ri-user-line"></i>
                                    {{ $user->level->label() }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <table class="table" style="border-color: transparent">
                        <tbody>
                            <tr>
                                <td>Email</td>
                                <td>{{ $user->email }}</td>
                            </tr>
                            <tr>
                                <td>Số điện thoại</td>
                                <td>{{ $user->phone }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <form action="{{ route('client.profile.update') }}" method="POST">
                @csrf
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Thông tin cá nhân</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group row">
                            <label for="full_name" class="col-xl-3 col-lg-3 col-form-label">Họ và tên</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       value="{{ $user->full_name }}">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="username" class="col-xl-3 col-lg-3 col-form-label">Tên đăng nhập</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="text" class="form-control" id="username" name="username"
                                       value="{{ $user->username }}" disabled>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="avatar" class="col-xl-3 col-lg-3 col-form-label">Ảnh đại diện</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="text" class="form-control mb-1" id="avatar" name="avatar"
                                       value="{{ old('avatar',$user->avatar) }}">
                                <div class="alert bg-warning fw-semibold text-dark">
                                    Website lấy link ảnh <a href="https://linkanh.xyz" target="_blank">Tại đây</a>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="email" class="col-xl-3 col-lg-3 col-form-label">Email</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="text" class="form-control" id="email" name="email"
                                       value="{{ $user->email }}">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-xl-3 col-lg-3 col-form-label">Số Điện Thoại</label>
                            <div class="col-lg-9 col-xl-6">
                                <div class="input-group">
                                    <div class="input-group-prepend"><span class="input-group-text"><i
                                                class="ri-phone-line"></i></span></div>
                                    <input type="text" id="phone" name="phone" class="form-control"
                                           value="{{ old('phone',$user->phone)  }}" minlength="8" maxlength="11">
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="old_password" class="col-xl-3 col-lg-3 col-form-label">Mật khẩu cũ</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="password" class="form-control" id="old_password" name="old_password">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="new_password" class="col-xl-3 col-lg-3 col-form-label">Mật khẩu mới</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="password" class="form-control" id="new_password" name="new_password">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="confirm_password" class="col-xl-3 col-lg-3 col-form-label">Xác nhận mật
                                khẩu</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="password" class="form-control mb-1" id="confirm_password"
                                       name="confirm_password">
                                <div class="alert bg-pink text-white">
                                    Vui lòng chọn mật khẩu tối thiểu 8 ký tự, bao gồm chữ cái, chữ số và kí tự đặc
                                    biệt, không được chứa tên đăng nhập
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-lg-3 col-xl-3"></div>
                            <div class="col-lg-9 col-xl-9">
                                <button type="submit" class="btn btn-success">Lưu thông tin</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
