<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\Tenant;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use App\Enums\BaseStatusEnum;

class PostController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $posts = Post::query()
                ->with(['tenant'])
                ->select('id','title', 'tenant_id', 'pin', 'status', 'created_at', 'type');

            return DataTables::of($posts)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.posts.edit', $row->id);
                    $deleteUrl = route('supper-admin.posts.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('title', function ($row) {
                    return '<a href="'.route('supper-admin.posts.edit', $row->id).'" class="text-plain">'.$row->title.'</a>';
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->editColumn('type', function ($row) {
                    return $row->type->toHtml();
                })
                ->editColumn('tenant', function ($row) {
                    return '<span class="badge bg-purple">'.$row->tenant->domain.'</span>' ?? 'N/A';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->rawColumns(['action', 'status', 'tenant', 'type','title'])
                ->make(true);
        }

        return view('supper-admin.posts.index');
    }

    public function create()
    {
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.posts.create', compact('tenants'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tenant_id' => 'required|exists:tenants,id',
            'status' => 'nullable|string|max:60|in:published,draft,pending',
            'image' => 'nullable',
            'pin' => 'nullable|boolean',
            'type'=> 'string|in:post,popup'
        ]);

        DB::beginTransaction();
        try {
            if (!empty($validated['pin']) && $validated['pin']) {
                Post::query()
                    ->where('tenant_id', $validated['tenant_id'])
                    ->where('pin', true)
                    ->update(['pin' => false]);
            }

            Post::query()->create($validated);
            DB::commit();

            return $this->httpResponse()->setNextUrl(route('supper-admin.posts.index'))->withCreatedSuccessMessage();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('Lỗi khi thêm bài viết: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi thêm mới bài viết. Vui lòng thử lại!']);
        }
    }

    public function edit(string|int $id)
    {
        $post = Post::query()->findOrFail($id);
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.posts.edit', [
            'dataEdit' => $post,
            'tenants' => $tenants,
        ]);
    }

    public function update(Request $request, string|int $id)
    {
        $post = Post::query()->findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tenant_id' => 'required|exists:tenants,id',
            'status' => 'nullable|string|max:60',
            'image' => 'nullable',
            'pin' => 'nullable|boolean',
            'type'=> 'string'
        ]);

        DB::beginTransaction();

        try {
            if (!empty($validated['pin']) && $validated['pin']) {
                Post::query()
                    ->where('tenant_id', $post->tenant_id)
                    ->where('id', '!=', $post->id)
                    ->where('pin', true)
                    ->update(['pin' => false]);
            }
            $post->update($validated);

            DB::commit();

            return $this->httpResponse()->setNextUrl(route('supper-admin.posts.index'))->withUpdatedSuccessMessage();
        } catch (\Throwable $e) {
            DB::rollBack();

            Log::error('Lỗi khi cập nhật bài viết', [
                'error' => $e->getMessage(),
                'post_id' => $id,
                'user_id' => auth()->id(),
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi cập nhật bài viết. Vui lòng thử lại sau.']);
        }
    }


    public function destroy(string|int $id)
    {
        $post = Post::query()->findOrFail($id);

        try {
            $post->delete();
            return $this->httpResponse()->setNextUrl(route('supper-admin.posts.index'))->withDeletedSuccessMessage();
        } catch (\Exception $exception) {
            Log::error('Lỗi khi xóa bài viết: ' . $exception->getMessage());
            return response()->json(['message' => 'Lỗi khi xóa bài viết'], 500);
        }
    }
}
