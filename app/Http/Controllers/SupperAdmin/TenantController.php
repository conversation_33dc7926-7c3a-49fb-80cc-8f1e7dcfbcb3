<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class TenantController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Tenant::query()
                ->select('id', 'domain', 'status', 'created_at', 'verified');

            return DataTables::of($data)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.tenants.edit', $row->id);
                    $deleteUrl = route('supper-admin.tenants.destroy', $row->id);
                    $settingUrl = route('supper-admin.tenants.settings.edit', $row->id);

                    return '
                        <div class="dropdown">
                            <a href="#" role="button" id="datatable-actions' . $row->id . '" data-bs-toggle="dropdown" aria-expanded="false" class="btn-action text-primary">
                                <i class="ri-more-2-fill"></i>
                            </a>

                            <ul class="dropdown-menu" aria-labelledby="datatable-actions' . $row->id . '">
                             <li>
                                    <a class="dropdown-item" href="' . $settingUrl . '" title="Sửa">
                                        Cài đặt
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="' . $editUrl . '" title="Sửa">
                                        Chỉnh sửa
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item btn-delete" href="#" data-id="' . $row->id . '" data-url="' . $deleteUrl . '" title="Xóa">
                                        Xóa bỏ
                                    </a>
                                </li>
                            </ul>
                        </div>
                    ';
                })
                ->editColumn('domain', function ($row) {
                    return '<span class="text-plain">' . $row->domain . '</span>';
                })
                ->editColumn('verified', function ($row) {
                    return $row->verified == 1
                        ? '<span class="badge bg-success"><i class="ri-check-line"></i>Đã xác thực</span>'
                        : '<span class="badge bg-danger"><i class="ri-close-line"></i> Chưa xác thực</span>';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->rawColumns(['action', 'status', 'domain', 'verified'])
                ->make(true);
        }
        return view('supper-admin.tenants.index');
    }

    public function edit(Request $request, string|int $id)
    {
        $tenant = Tenant::query()->findOrFail($id);
        $users = User::query()
            ->select('id', 'username', 'status')
            ->where('status',UserStatusEnum::ACTIVE->value)
            ->get();
        return view('supper-admin.tenants.edit',[
            'dataEdit' =>  $tenant,
            'users' => $users,
        ]);
    }
}
