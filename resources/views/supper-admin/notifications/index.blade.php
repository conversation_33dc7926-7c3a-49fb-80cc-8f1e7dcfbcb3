@extends('admin.layouts.app')

@section('title', 'Danh sách thông báo')
@push('css')
    @include('admin.partials.datatables.style')
@endpush
@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            Danh sách thông báo
        </h4>
        <div class="">
            <a href="{{ route('supper-admin.notifications.create') }}" class="btn btn-success">
                <i class="ri-add-line"></i>
                Thêm mới
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatables">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tiêu đề</th>
                        <th>Nội dung</th>
                        <th><PERSON><PERSON><PERSON> t<PERSON></th>
                        <th>Website</th>
                        <th><PERSON><PERSON> t<PERSON></th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('supper-admin.notifications.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'title', name: 'title'},
                    {data: 'content', name: 'content'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'tenant', name: 'tenant'},
                    {data: 'action', name:'action'}
                ],
                order: [[0, 'desc']],
            });
        });
    </script>
@endpush
