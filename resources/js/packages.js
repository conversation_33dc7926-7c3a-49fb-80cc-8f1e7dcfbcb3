$(() => {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        cache: false
    });

    const $apiProviderID = $('#api_provider_id');
    const $serviceID = $('#service_id');

    const $serviceName = $('.service-name');
    const $min = $('#min');
    const $max = $('#max');
    const $rate = $('#rate');
    const $type = $('#service_type');
    const $refill = $('#refill');
    const $dripFeed = $('#drip_feed');
    const $cancel = $('#cancel');

    const initialProviderId = $apiProviderID.data('selected') || $apiProviderID.val();
    const initialServiceId = $serviceID.data('selected');
    const initialServiceName = $serviceID.data('selected-name');

    const resetServiceInfo = () => {
        $serviceID.html('<option value="0">Chọn dịch vụ</option>').val('0').trigger('change');
        $serviceName.val('');
        $min.val('');
        $max.val('');
        $rate.val('');
        $type.val('');
        $refill.prop('checked', false);
        $dripFeed.prop('checked', false);
        $cancel.prop('checked', false);
    };

    const fillServiceInfo = (service = {}) => {
        $serviceName.val(service.name || '');
        $min.val(service.min || '');
        $max.val(service.max || '');
        $rate.val(service.rate || '');
        $type.val(service.type || '');
        $refill.prop('checked', !!service.refill);
        $dripFeed.prop('checked', !!service.drip_feed);
        $cancel.prop('checked', !!service.cancel);
    };

    const formatTemplate = ({ id, text }) => {
        id = id || '';
        return $(`<span><span class="dropdown-item-indicator">${id} &#45;</span>${text}</span>`);
    };

    // Hàm khởi tạo service select
    const initializeServiceSelect = (providerId, preloadServiceId = null, preloadServiceName = null) => {
        const url = $apiProviderID.data('url');

        if (preloadServiceId && preloadServiceId !== '0' && preloadServiceName) {
            const preloadOption = new Option(preloadServiceName, preloadServiceId, true, true);
            $serviceID.html('').append(preloadOption);
        } else {
            $serviceID.html('<option value="0">Chọn dịch vụ</option>');
        }

        Base.select($serviceID, {
            ajax: {
                url: url,
                delay: 250,
                cache: true,
                data: (params) => ({
                    provider_id: providerId,
                    q: params.term,
                    page: params.page || 1,
                }),
                processResults: ({ data }) => {
                    const services = data.data.services || data.data;

                    return {
                        results: $.map(services, (item) => ({
                            id: item.service || item.id,
                            text: item.name,
                            raw: item
                        })),
                        pagination: {
                            more: !!data.next_page_url && Object.keys(services).length > 0,
                        },
                    };
                },
            },
            templateResult: formatTemplate,
            templateSelection: formatTemplate,
        }, true);

        if (preloadServiceId && preloadServiceId !== '0') {
            loadServiceDetails(providerId, preloadServiceId);
        }

        $serviceID.off('select2:select').on('select2:select', function (e) {
            const service = e.params.data?.raw;
            if (service) {
                fillServiceInfo(service);
            } else {
                const serviceId = e.params.data.id;
                if (serviceId && serviceId !== '0') {
                    loadServiceDetails(providerId, serviceId);
                } else {
                    resetServiceInfo();
                }
            }
        });
    };

    const loadServiceDetails = (providerId, serviceId) => {
        const url = $apiProviderID.data('url');

        $.ajax({
            url: url,
            data: {
                provider_id: providerId,
                service_id: serviceId,
                q: '',
                page: 1
            },
            success: function(response) {
                if (response.data && response.data.length > 0) {
                    const service = response.data.find(item => item.service == serviceId);
                    if (service) {
                        fillServiceInfo(service);
                    }
                }
            },
            error: function() {
                console.log('Không thể load thông tin service');
            }
        });
    };

    $apiProviderID.on('change', function () {
        const providerId = $(this).val();

        resetServiceInfo();

        if (!providerId || providerId === '0') return;

        initializeServiceSelect(providerId);
    });

    const initializeOnLoad = () => {
        if (initialProviderId && initialProviderId !== '0') {
            if ($apiProviderID.val() !== initialProviderId) {
                $apiProviderID.val(initialProviderId).trigger('change.select2');
            }

            initializeServiceSelect(initialProviderId, initialServiceId, initialServiceName);
        }
    };

    setTimeout(initializeOnLoad, 100);
});
