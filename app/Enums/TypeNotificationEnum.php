<?php

namespace App\Enums;

enum TypeNotificationEnum: string
{
    case SYSTEM = 'system';

    case USER = 'user';

    case DEPOSIT = 'deposit';

    case REFUND = 'refund';

     case PLUS_MONEY = 'plus_money';

     case MINUS_MONEY = 'minus_money';
    public function label(): string
    {
        return match ($this) {
            self::SYSTEM => 'Hệ thống',
            self::USER => 'Người dùng',
            self::DEPOSIT => 'Thanh toán',
            self::REFUND => 'Hoàn tiền',
        };
    }
}
