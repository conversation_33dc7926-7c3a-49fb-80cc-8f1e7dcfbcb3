"use strict";

import { definedColumns, xAjax, reloadTable , components} from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.stt,
    definedColumns.name,
    definedColumns.balance,
    definedColumns.status,
    definedColumns.created_at,
    definedColumns.action((data, type, row) => {
      return components.btn_edit(row, '/supper-admin/providers') + components.btn_delete(row, '/supper-admin/providers');
    }),
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/supper-admin/providers/ajax/data`),
    order: [[ 0, "desc" ]],
    columns: columns
  });
});
