@extends('admin.layouts.app')

@section('title', 'Đơn hàng')
@push('css')
    @include('admin.partials.datatables.style')
@endpush
@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            <PERSON>h sách đơn hàng
        </h4>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatables">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Code</th>
                        <th>S<PERSON> lượng</th>
                        <th>Giá</th>
                        <th>Tổng số tiền</th>
                        <th>Trạng thái</th>
                        <th>Ng<PERSON>y tạo</th>
                        <th>Website</th>
                        <th>Thao tác</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('supper-admin.orders.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'order_code', name: 'order_code'},
                    {data: 'count', name: 'count'},
                    {data: 'price', name: 'price'},
                    {data: 'total_payment', name: 'total_payment'},
                    {data: 'status', name: 'status'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'tenant', name: 'tenant.domain'},
                    {data: 'action', name: 'action'}
                ],
                order: [[0, 'desc']],
            });
        });
    </script>
@endpush
