<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use App\Enums\PostTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Post extends Model
{
    protected $table = 'posts';
    protected $fillable = [
        'tenant_id',
        'title',
        'content',
        'image',
        'pin',
        'status',
        'type',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'type' => PostTypeEnum::class,
        'pin' => 'boolean',
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

}
