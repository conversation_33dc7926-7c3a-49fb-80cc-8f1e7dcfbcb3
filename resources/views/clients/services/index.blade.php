@extends('clients.layouts.app')

@section('title')
    {{ $category->name }}
@endsection

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="card">
        <ul class="nav nav-tabs nav-tabs-custom nav-success" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" data-bs-toggle="tab" href="#shopping-tab" role="tab" aria-selected="false"
                   tabindex="-1">
                    <i class="ri-shopping-bag-4-line"></i>
                    Mua g<PERSON>i dịch vụ
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link " data-bs-toggle="tab" href="#history-shopping" role="tab" aria-selected="true">
                    <i class="ri-history-line"></i>
                    Lịch sử mua
                </a>
            </li>
        </ul>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane fade active show" id="shopping-tab" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-group">
                                        <input type="hidden" name="url" id="url">
                                        <label for="uid" class="form-label">ID hoặc link bài viết cần chạy</label>
                                        <input type="text" class="form-control" id="uid" name="uid" autocomplete="off"
                                               placeholder="Vui lòng nhập đúng link, sai link không hoàn tiền">
                                    </div>
                                    <div class="form-group">
                                        <label for="" class="form-label">Chọn gói dịch vụ</label>
                                        @foreach($packages as $item)
                                            <div class="radio radio-server">
                                                <label>
                                                    <input type="radio" name="server" value="server_{{ $item->id }}">
                                                    Gói {{ $item->id }}
                                                </label>
                                            </div>
                                        @endforeach

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-purple">
                                    <h4 class="card-title text-white mb-0">
                                        Lưu ý
                                    </h4>
                                </div>
                                <div class="card-body">

                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header bg-purple">
                                    <h4 class="card-title text-white mb-0">
                                        Video hướng dẫn
                                    </h4>
                                </div>
                                <div class="card-body">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="history-shopping" role="tabpanel">
                    <div class="row">
                        <div class="col-12">
                            @include('clients.services.partials.history')
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    @include('admin.partials.datatables.script')
    <script>
        let platform = @json($platform);
        let category = @json($category);
        $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            if (e.target.getAttribute('href') === '#history-shopping') {
                if (typeof updateHistoryFilter === 'function') {
                    updateHistoryFilter(platform, category);
                }
            }
        });
    </script>
@endpush
