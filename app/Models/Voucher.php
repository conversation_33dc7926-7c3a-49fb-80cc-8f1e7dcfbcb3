<?php

namespace App\Models;

use App\Enums\VoucherTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Voucher extends Model
{
    protected $table = 'vouchers';

    protected $fillable = [
        'tenant_id',
        'code',
        'type',
        'value',
        'start_date',
        'end_date',
        'is_active'
    ];

    protected $casts = [
        'type' => VoucherTypeEnum::class,
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'value' => 'decimal:2'
    ];

    /**
     * Relationship với Tenant
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Kiểm tra voucher có còn hiệu lực không
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();

        if ($this->start_date && $now->lt($this->start_date)) {
            return false;
        }

        if ($this->end_date && $now->gt($this->end_date)) {
            return false;
        }

        return true;
    }

    /**
     * Tự động tạo code voucher nếu chưa có
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($voucher) {
            if (empty($voucher->code)) {
                do {
                    $code = 'VC' . strtoupper(uniqid());
                } while (self::where('code', $code)->exists());

                $voucher->code = $code;
            }
        });
    }

    /**
     * Scope để lấy voucher còn hiệu lực
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope để lấy voucher theo tenant
     */
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }
}
