<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $payments = Payment::query()
                ->with(['user', 'tenant'])
                ->select('payments.*');

            return DataTables::of($payments)
                ->editColumn('status', function ($row) {
                    $statusClass = [
                        'pending' => 'bg-warning',
                        'completed' => 'bg-success',
                        'failed' => 'bg-danger',
                        'refunded' => 'bg-info',
                    ];
                    
                    $class = $statusClass[$row->status] ?? 'bg-secondary';
                    return '<span class="badge ' . $class . '">' . ucfirst($row->status) . '</span>';
                })
                ->editColumn('amount', function ($row) {
                    return number_format($row->amount, 0, ',', '.') . ' đ';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y H:i:s');
                })
                ->editColumn('user', function ($row) {
                    return $row->user->name ?? 'N/A';
                })
                ->editColumn('tenant', function ($row) {
                    return '<span class="badge bg-purple">'.$row->tenant->domain.'</span>' ?? 'N/A';
                })
                ->addColumn('details', function ($row) {
                    return '<button type="button" class="btn btn-sm btn-info view-details" data-id="'.$row->id.'">
                        <i class="ri-eye-line"></i> Chi tiết
                    </button>';
                })
                ->rawColumns(['status', 'tenant', 'details'])
                ->make(true);
        }

        return view('supper-admin.payments.index');
    }

    public function show($id)
    {
        $payment = Payment::with(['user', 'tenant'])->findOrFail($id);
        return response()->json([
            'id' => $payment->id,
            'reference' => $payment->reference,
            'amount' => number_format($payment->amount, 0, ',', '.') . ' đ',
            'status' => ucfirst($payment->status),
            'payment_method' => $payment->payment_method,
            'description' => $payment->description,
            'user' => $payment->user->name ?? 'N/A',
            'tenant' => $payment->tenant->domain ?? 'N/A',
            'created_at' => $payment->created_at->format('d/m/Y H:i:s'),
            'updated_at' => $payment->updated_at->format('d/m/Y H:i:s'),
            'transaction_data' => $payment->transaction_data ? json_encode($payment->transaction_data, JSON_PRETTY_PRINT) : null,
        ]);
    }
}
