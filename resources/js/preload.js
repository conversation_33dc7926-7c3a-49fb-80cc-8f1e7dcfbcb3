const { get } = require("jquery");

// Constants và cấu hình
const REGEX_PATTERNS = {
    FACEBOOK_DOMAINS: /facebook\.com|fb\.watch/,
    INSTAGRAM_DOMAIN: /instagram\.com/,
    TIKTOK_VIDEO: /tiktok\.com\/([^\/]+)\/video\/([0-9]+)/,
    TIKTOK_PROFILE: /tiktok\.com\/@([\w\-._]+)/,
    NUMERIC_ID: /^\d+$/,
    UID_FORMAT: /\w+_\d+/,
    PFBID_FORMAT: /(^[\d_]+$|^pfbid\w+)/
};

const FB_URL_PATTERNS = {
    POSTS_VIDEOS: /(posts|videos)\/([\d\w]+)/,
    FBID: /fbid=([\d\w]+)/,
    PHOTOS: /\/photos\//,
    PERMALINK: /\/permalink\/([0-9]+)/,
    WATCH_LIVE_V: /watch\/live\/\?v=([0-9]+)/,
    WATCH_V: /watch\/\?v=([0-9]+)/,
    WATCH_LIVE_V2: /v=(\d+)/,
    STORIES: /\/stories\//,
    REEL: /reel\/(\d+)/,
    EVENTS: /events\/(\d+)/,
    ID_PARAM: /\?id=(\d+)/,
    MULTI_PERMALINKS: /multi_permalinks=([0-9]+)/,
    SHARE_WATCH: /(fb\.watch|facebook\.com\/share\/)/
};

const INSTAGRAM_PATTERNS = {
    POST: /com\/p\/([a-zA-Z0-9_.-]+)/,
    PROFILE: /com\/([a-zA-Z0-9_.-]+)/
};

// Utility functions
const formatLink = (link) => {
    link = link.toString().trim();
    if (link.endsWith('#')) link = link.slice(0, -1);

    const httpsIndex = link.indexOf('https:');
    if (httpsIndex > 0) link = link.substring(httpsIndex);

    if (!link.includes('?') ||
        /\?fbid|story_fbid|watch\/|story\.php|multi_permalinks/.test(link)) {
        return link;
    }
    return link.split("?")[0];
};

const extractBetween = (str, start, end) => {
    try {
        const parts = str.split(start);
        if (parts.length < 2) return "";
        return parts[1].split(end)[0];
    } catch {
        return "";
    }
};

const makeApiCall = async (url, data) => {
    return new Promise((resolve) => {
        $.ajax({
            type: "POST",
            url: url,
            data: data,
            success: resolve,
            error: () => resolve({ status: false, msg: 'Lỗi mạng' })
        });
    });
};

const showLoadingDialog = (message = 'Đang xử lý...') => {
    return swalTimeOut(message, 'info', 10000, 'Thử lại', 'Huỷ', false);
};

// Main functions
const extractFacebookUid = (url) => {
    const patterns = FB_URL_PATTERNS;

    if (url.includes("/posts/") || url.includes("/videos/")) {
        const match = url.match(patterns.POSTS_VIDEOS);
        return match?.[2];
    }

    if (url.includes("fbid=")) {
        const match = url.match(patterns.FBID);
        return match?.[1];
    }

    if (url.includes("story_fbid=")) {
        return extractBetween(url, "story_fbid=", "&");
    }

    if (url.includes("/photos/")) {
        const parts = url.split("/");
        return parts[parts.length - 2];
    }

    const simplePatterns = [
        patterns.PERMALINK,
        patterns.WATCH_LIVE_V,
        patterns.WATCH_V,
        patterns.REEL,
        patterns.EVENTS,
        patterns.ID_PARAM,
        patterns.MULTI_PERMALINKS
    ];

    for (const pattern of simplePatterns) {
        const match = url.match(pattern);
        if (match) return match[1];
    }

    if (url.match(/watch\/live/) && url.match(/v=\d+/)) {
        const match = url.match(patterns.WATCH_LIVE_V2);
        return match?.[1];
    }

    if (url.match(patterns.STORIES)) {
        return url.split('?')[0];
    }

    return null;
};

async function getPostUid(input, silent = true) {
    const data = formatLink(input);

    // Check if already in correct format
    if (REGEX_PATTERNS.PFBID_FORMAT.test(data)) {
        return [true, data];
    }

    let uid;

    if (REGEX_PATTERNS.FACEBOOK_DOMAINS.test(data)) {
        uid = extractFacebookUid(data);

        // Handle special cases that need API call
        if (!uid && FB_URL_PATTERNS.SHARE_WATCH.test(data)) {
            if (!silent) {
                showLoadingDialog().then(confirm => {
                    if (confirm) $('#uid').trigger('change');
                });
            }

            const response = await makeApiCall('/facebook/get_link_uid', { link: data, type });
            if (!silent) swalClose();

            return response.status ? [true, response.msg] : [false, response.msg];
        }
    }
    else if (REGEX_PATTERNS.INSTAGRAM_DOMAIN.test(data)) {
        const match = data.match(INSTAGRAM_PATTERNS.POST) || data.match(INSTAGRAM_PATTERNS.PROFILE);
        uid = match?.[1];
    }

    if (!uid) return [false, 'Không nhận ra uid'];

    // Handle reply comment if needed
    if ($('#switch_uid_reply').is(':checked')) {
        const replyMatch = input.match(/(?:reply_comment_id|comment_id)=([0-9]+)/);
        if (replyMatch) uid += '_' + replyMatch[1];
    }

    return [true, uid];
}

async function getProfileInfo(input, silent = true) {
    const cleanInput = input.trim();

    if (REGEX_PATTERNS.FACEBOOK_DOMAINS.test(cleanInput) || cleanInput.includes('fb.com')) {
        return await handleFacebookProfile(cleanInput, silent);
    }

    if (REGEX_PATTERNS.INSTAGRAM_DOMAIN.test(cleanInput)) {
        return handleInstagramProfile(cleanInput);
    }

    // Default case - treat as username
    if (!silent) {
        showLoadingDialog().then(confirm => {
            if (confirm) $('#link').trigger('change');
        });
    }
    return await getFbInfoFromUsername(cleanInput);
}

async function handleFacebookProfile(input, silent) {
    // Handle profile.php format
    if (input.includes("profile.php") &&
        !(typeof type !== "undefined" && type === 'review')) {
        const match = input.match(/id=(\d+)/);
        if (match) {
            return [true, { id: match[1], name: 'Tên khách hàng' }];
        }
    }

    // Handle groups
    const groupMatch = input.match(/groups\/(\d+)/);
    if (groupMatch) {
        return [true, { id: groupMatch[1], name: 'Tên Nhóm' }];
    }

    // Extract username from various URL formats
    let username = "";
    if (input.includes("/posts/")) {
        username = extractBetween(input, "facebook.com/", "/posts/");
    } else if (input.includes("/videos/")) {
        username = extractBetween(input, "facebook.com/", "/videos/");
    } else {
        const match = input.match(/(facebook|fb)\.com\/(.*)/);
        username = match?.[2] || "";
    }

    if (!username) return [false, 'Không nhận ra link'];

    if (!silent) {
        showLoadingDialog().then(confirm => {
            if (confirm) $('#link').trigger('change');
        });
    }

    return await getFbInfoFromUsername(username);
}

function handleInstagramProfile(input) {
    if (!input.includes("/p/")) {
        const username = extractBetween(input, "instagram.com/", "/");
        return [true, { id: username, name: 'Tên khách hàng' }];
    }
    return [false, 'Không nhận ra link'];
}

async function getCommentLink(input) {
    // Already in correct format
    if (REGEX_PATTERNS.UID_FORMAT.test(input)) {
        return [true, input];
    }

    const [success, postId] = await getPostUid(input, false);
    if (success) {
        const commentMatch = input.match(/(?:reply_comment_id|comment_id)=([0-9]+)/);
        if (commentMatch) {
            return [true, `${postId}_${commentMatch[1]}`];
        }
    }

    return [false, 'Không nhận dạng được link!'];
}

async function getTiktokLink(link, silent = true) {
    const formattedLink = formatLink(link);

    if (REGEX_PATTERNS.TIKTOK_VIDEO.test(formattedLink) ||
        REGEX_PATTERNS.TIKTOK_PROFILE.test(formattedLink)) {
        return [true, formattedLink];
    }

    if (!silent) {
        showLoadingDialog('Đang get link...').then(confirm => {
            if (confirm) $('#tiktok_post_link').trigger('change');
        });
    }

    // Clean up link
    let cleanLink = link;
    const httpsIndex = cleanLink.indexOf('https');
    if (httpsIndex > 0) cleanLink = cleanLink.substring(httpsIndex);
    if (cleanLink.includes(' ')) cleanLink = cleanLink.split(' ')[0];

    const response = await makeApiCall('/api/get-tiktok-info', { link: cleanLink });
    return response.status === 1 ? [true, response.msg] : [false, response.msg];
}

function reFormatUrl(input) {
    let cleanInput = input.endsWith('#') ? input.slice(0, -1) : input;

    const httpsIndex = cleanInput.indexOf('https:');
    if (httpsIndex > 0) {
        cleanInput = 'https:' + cleanInput.split('https:')[1];
    }

    if (typeof type !== "undefined" &&
        !/youtube|livestream_v2|view_other|video|live_instagram/.test(type) &&
        cleanInput.includes('?')) {
        return [true, formatLink(cleanInput)];
    }

    return [true, cleanInput];
}

async function getFbInfoFromUsername(username) {
    try {
        if (REGEX_PATTERNS.NUMERIC_ID.test(username.toString())) {
            return [true, { id: username, name: 'Tên khách hàng' }];
        }

        const response = await makeApiCall('/api/get-uid', {
            username,
            need_page_id: $('#need_page_id').is(':checked') ? 1 : ''
        });

        return response.status === 1 ? [true, response.data] : [false, response.msg];
    } catch (error) {
        console.warn('getFbInfoFromUsername error:', error);
        return [false, 'Lỗi mạng, vui lòng thử lại'];
    }
}

// Event handlers
$(document).on("change", "#uid", async function () {
    const inputText = $(this).val().trim();

    if ($('#url').length) $('#url').val(inputText);
    if (!inputText) return;

    const [success, uid] = await getPostUid(inputText, false);
    const cleanUid = uid?.replace(/#+$/, '');

    if (success && cleanUid) {
        $("#uid").val(cleanUid);
        if (typeof toastr !== "undefined") {
            Base.showSuccess("Cập nhật objectId thành công");
        }
    } else {
        if (typeof toastr !== "undefined") {
            Base.showError(uid || "Không nhận ra uid");
        }
    }
});
$(document).on("change", ".post-id", async function () {
    let [success, uid] = await getPostUid($(this).val(), false);

    if (success) $(this).val(uid);
});
$(document).on("change", "#comment_link", async function () {
    let [success, data] = await getCommentLink($(this).val());
    if (!success) return Base.showError(data);

    $(this).val(data);
    Base.showSuccess('Cập nhật comment ID thành công');
});

// Datatables

var datatableLog,
    ajaxUrl;
var currency = 'VND'
function replaceAll(str, find, replace) {
    try {
        return str.replace(new RegExp(find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1"), 'g'), replace);
    } catch (ex) {
        return "";
    }
}
function formatMoney(input, suffix = '', roundNumber = true) {
    if (!suffix) suffix = '';
    if (suffix == 'd') suffix = '₫';

    if (currency === 'usd') {
        roundNumber = false;
        if (suffix === '₫') suffix = '$';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'USD')
    } else if (currency === 'baht') {
        roundNumber = false;
        if (suffix === '₫') suffix = '฿';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'Baht')
    }

    var number = parseFloat(input);
    if (roundNumber) number = Math.floor(number);
    if (isNaN(number)) return input;

    if (currency === 'usd') number = round(number, 6);
    if (currency === 'baht') number = round(number, 4);

    // If float
    if (number && number.toString().includes('.')) {
        let parts = number.toString().split('.');
        return formatMoney(parts[0]) + '.' + parts[1] + suffix;
    }
    else {
        return number.toLocaleString() + suffix;
    }
}

const components = {
    default: function (text) {
        return `<span class="text-success">${text}</span>`;
    },

    // Button components
    btn_delete_vip_expired: function (full) {
        return `<button data-id="${full.id}" class="btn btn-icon btn-delete-expired" data-toggle="tooltip" title="Xóa">
              <i class="fa fa-trash text-danger"></i>
            </button>`;
    },

    btn_refund_vip: function (vip) {
        return `<button class="btn btn-icon btn-refund-vip" data-toggle="tooltip" title="Hoàn tiền"
               data-id="${vip.id}" data-type="${vip.type}" data-server="${vip.server}">
                <i class="fa fa-trash text-danger"></i>
            </button>`;
    },

    btn_view_log: function (full) {
        return `<button class="btn btn-icon btn-view-log" data-toggle="tooltip" title="Xem log"
              data-id="${full.id}">
                <i class="fa-solid fa-chart-line text-primary"></i>
            </button>`;
    },

    btn_log_priority: function (row) {
        return `<button class="btn btn-icon btn-log-priority" data-toggle="tooltip" title="Ưu tiên" data-id="${row.id}">
                <i class="fa fa-angle-up text-primary"></i>
            </button>`;
    },

    btn_delete_db: function (full) {
        return `<button data-id="${full.id}" data-type="${full.type}" class="btn btn-icon btn-delete-db" data-toggle="tooltip" title="Xóa DB">
                <i class="fa fa-trash text-danger"></i>
            </button>`;
    },

    btn_re_check: function (buff) {
        return `<button class="btn btn-icon btn-re-check" data-id="${buff.id}" data-type="${buff.type}" data-server="${buff.server}" data-toggle="tooltip" title="Tiếp tục chạy">
              <i class="fas fa-clipboard-check text-primary"></i>
            </button>`;
    },

    btn_refund_buff: function (full) {
        return `<button data-id="${full.id}" data-type="${full.type}" class="btn btn-icon btn-refund-buff" data-toggle="tooltip" title="Hoàn tiền">
                <i class="fa fa-trash text-danger"></i>
            </button>`;
    },

    btn_warranty_buff: function (full) {
        return `<button data-id="${full.id}" class="btn btn-icon btn-warranty-buff" data-toggle="tooltip" title="Bảo Hành">
                <i class="fas fa-sync-alt text-primary"></i>
            </button>`;
    },

    btn_delete_buff: function (full) {
        return `<button data-id="${full.id}" data-type="${full.type}" class="btn btn-icon btn-delete-buff" title="Xóa để mua lại">
                <i class="fa fa-trash text-danger"></i>
            </button>`;
    },

    btn_edit: function (row, className = 'btn-edit') {
        return `<button data-id="${row.id}" class="btn btn-icon ${className}" title="Sửa">
                <i class="fas fa-edit text-success"></i>
            </button>`;
    },

    btn_delete: function (row, className = 'btn-delete') {
        return `<button data-id="${row.id}" class="btn btn-icon ${className}" title="Xóa">
                <i class="fa fa-trash text-danger"></i>
            </button>`;
    },

    btn_sync: function (id, className = 'btn-sync', title = 'Đồng bộ') {
        return `<button type="button" data-id="${id}" class="btn btn-icon ${className}" title="${title}">
                <i class="text-primary fa fa-repeat"></i>
            </button>`;
    },

    btn_edit_log: function (full) {
        return `<button data-id="${full.id}" data-type="${full.type}" class="btn btn-icon btn-edit-log" data-toggle="tooltip" title="Sửa">
                <i class="fas fa-edit text-success"></i>
            </button>`;
    },

    btn_edit_vip: function (vip) {
        return `<button data-id="${vip.id}" class="btn btn-icon btn-edit-vip" data-toggle="tooltip" title="Sửa">
              <i class="fas fa-edit text-success"></i>
            </button>`;
    },

    btn_add_post: function (vip) {
        return `<button data-id="${vip.id}" class="btn btn-icon btn-add-post" data-toggle="tooltip" title="Bù bài lỗi">
              <i class="fas fa-plus-circle text-success"></i>
            </button>`;
    },

    btn_edit_bot: function (id) {
        return `<button class="btn btn-icon btn-edit-bot" data-id="${id}" data-toggle="tooltip" title="Sửa gói">
              <span class="fas fa-edit text-success"></span>
            </button>`;
    },

    btn_view_log_bot: function (bot) {
        return `<button class="btn btn-icon btn-view-bot-log" data-order_id="${bot.order_id}" data-toggle="tooltip" title="Xem log">
              <span class="fa fa-eye text-warning"></span>
            </button>`;
    },

    btn_extend_vip: function (vip) {
        return `<button class="btn btn-icon btn-extend-vip" data-toggle="tooltip" title="Gia hạn"
              data-id="${vip.id}" data-type="${vip.type}" data-server="${vip.server}">
              <i class="fas fa-clock text-success"></i>
            </button>`;
    },

    btn_check_proxy: function (proxy) {
        return `<button class="btn btn-icon btn-check-proxy" data-toggle="tooltip" title="Check Proxy" data-order_id="${proxy.order_id}">
              <i class="fas fa-check-circle text-primary"></i>
            </button>`;
    },

    // Badge components
    badge_success: (text) => `<span class="badge badge-success">${text}</span>`,
    badge_primary: (text) => `<span class="badge badge-primary">${text}</span>`,
    badge_warning: (text) => `<span class="badge badge-warning">${text}</span>`,
    badge_danger: (text) => `<span class="badge badge-danger">${text}</span>`,

    text_copyable: function (text) {
        return `<span class="copy-on-click text-success" data-title="Sao chép"
              data-toggle="tooltip" data-content="${text}">
              <i class="fa-regular fa-clipboard"></i>
              ${text}
            </span>`;
    },

    // Table components
    table: {
        id: function (data, typeT, full) {
            return `<span class="text-success text-bold id-${full.id}">${data}</span>`;
        },

        uid: function (data, type, full) {
            if (full && ['proxy', 'bank_voucher', 'services'].includes(full.type)) {
                return components.table.text_primary(data);
            }

            if (data === 'NULL') return ' ';

            if (full.link && full.link !== 'default') {
                if (!full.link.match(/^(http)/)) {
                    if (!full.link.toString().includes('.')) {
                        full.link = 'https://fb.com/' + full.link;
                    } else {
                        full.link = 'https://' + full.link;
                    }
                }

                if (data.length > 30) data = data.substring(0, 28) + '...';

                return `<a href="${full.link}" class="text-bold" target="_blank">${data}</a>`;
            } else {
                let prefix = 'fb.com';
                if (full.type.match(/instagram/)) prefix = 'instagram.com';
                if (full.type.match(/twitter/)) prefix = 'x.com';
                if (full.type.match(/tiktok/)) {
                    prefix = 'www.tiktok.com';
                    data = '@' + data;
                }
                return `<a href="https://${prefix}/${data}" class="text-bold" target="_blank">${data}</a>`;
            }
        },

        user: function (data) {
            return `<span class="text-warning text-bold">${data}</span>`;
        },

        server: function (data, t, log) {
            let mapping;
            if (log.type == 'view_other') {
                mapping = {
                    server_1: '600k phút - SV Thường',
                    server_2: '600k phút - SV Rẻ',
                    server_3: '15k tương tác',
                };
                data = mapping[data] || 'null';
            } else {
                if (data) data = data.replace('server_', 'Server ');
                if (!data) data = '';
            }
            return `<span class="text-danger text-bold">${data}</span>`;
        },

        content: function (data, typeT, full) {
            return `<div class="table-text-plain">${data}</div>`;
        },

        note: function (data, typeT, full) {
            return `<div class="table-text-plain note-${full.id}">${data}</div>`;
        },

        admin_note: function (data, typeT, full) {
            return `<div class="table-text-plain text-danger admin_note-${full.id}">${data}</div>`;
        },

        original: function (data, typeT, full) {
            return `<button class="btn btn-brand original-${full.id}">${data}</button>`;
        },

        present: function (data, typeT, full) {
            return `<button class="btn btn-success present-${full.id}">${data}</button>`;
        },

        price: function (data, typeT, full) {
            return `<span class="kt-badge kt-badge--success kt-badge--inline money-value">${formatMoney(full.price_current)}</span> 
              <span>${full.math || '+'}</span> 
              <span class="kt-badge kt-badge--danger kt-badge--inline money-value">${formatMoney(full.price || full.commission || 0)}</span> 
              <span>=</span> 
              <span class="kt-badge kt-badge--primary kt-badge--inline money-value">${formatMoney(full.price_left)}</span>`;
        },

        count: function (data, typeT, full) {
            return `<button class="btn btn-danger count-${full.id}">${data}</button>`;
        },

        package: function (data, typeT, full) {
            return `<button class="btn btn-danger package-${full.id}">${data}</button>`;
        },

        time: function (data) {
            if (!data) return ' ';
            return `<button class="btn btn-success">${moment(data).format('HH:mm:ss DD/MM/YYYY')}</button>`;
        },

        time_text: function (data) {
            if (!data) return ' ';
            return `<span class="text-success">${moment(data).format('HH:mm:ss DD/MM/YYYY')}</span>`;
        },

        status: function (data, typeT, full) {
            return getStatusHtml(full);
        },

        duration: function (data) {
            return `<button class="btn btn-danger btn-ssm">${data}</button>`;
        },

        vip_status: function (data, typeT, full) {
            return getStatusVipHtml(full);
        },

        type: function (data) {
            return `<button class="btn btn-primary">${data}</button>`;
        },

        comment: function (data) {
            if (typeof data == 'string') console.log('data string', data);
            return components.click_to_view(data.content);
        },

        text: function (data) {
            return `<span class="text-bold">${data}</span>`;
        },

        domain_status: function (data) {
            const status = domainStatus[data];
            return `<span class="badge b-status-${data}">${status}</span>`;
        },

        text_success: function (data) {
            if (data == 0 && data.toString().length) data = '0';
            if (!data) data = '';
            return `<span class="text-success text-bold">${data}</span>`;
        },

        text_primary: function (data) {
            if (data == 0 && data.toString().length) data = '0';
            if (!data) data = '';
            return `<span class="text-primary text-bold">${data}</span>`;
        },

        text_info: function (data) {
            if (data == 0 && data.toString().length) data = '0';
            if (!data) data = '';
            return `<span class="text-info text-bold">${data}</span>`;
        },

        text_warning: function (data) {
            if (data == 0 && data.toString().length) data = '0';
            if (!data) data = '';
            return `<span class="text-warning text-bold">${data}</span>`;
        },

        text_money: function (data) {
            if (data == 0 && data.toString().length) data = 0;
            return `<span class="text-money text-bold">${formatMoney(data)}</span>₫`;
        },

        text_danger: function (data) {
            if (data == 0 && data.toString().length) data = '0';
            if (!data) data = '';
            return `<span class="text-danger text-bold">${data}</span>`;
        },

        like_count: function (data) {
            return `<span class="text-danger text-bold">${data}</span>`;
        },

        payment_mode: function (is_auto, t, full) {
            return `<span class='text-${is_auto ? 'danger' : 'success'} text-bold'>${parseInt(is_auto) ? ('Tự động' + (full.extra || '')) : 'Thủ công'}</span>`;
        },

        textarea: function (data) {
            return `<textarea class="form-control in-table">${data}</textarea>`;
        },

        changed_money: function (data) {
            return `<span class="text-danger text-bold">${formatMoney(data, " VNĐ") + (data < 0 ? ' (trừ đi)' : '')}</span>`;
        },

        number: function (data) {
            return `<span class="text-danger text-bold">${formatMoney(data, "")}</span>`;
        },

        money_vnd: function (data) {
            return `<span class="text-danger text-bold">${formatMoney(data, " VNĐ")}</span>`;
        },
    },

    click_to_view: function (content) {
        if (!content || content == '{}') return ' ';

        return `<div class="click-to-view">
              <button class="btn btn-click-view">Xem</button>
              <textarea class="form-control" style="display: none" rows="4">${content}</textarea>
            </div>`;
    },

    enable: enable => enable ? 'Không' : 'Có',
    bool: value => components.table.text_primary(value ? 'Có' : 'Không'),

    time_expired: function (data) {
        if (data === null || typeof data == "undefined") {
            return `<button class="btn btn-danger text-bold">Hết hạn</button>`;
        }
        if (isExpired(data)) {
            return `<button class="btn btn-danger text-bold">Hết hạn (${moment(data).format('DD/MM/YYYY')})</button>`;
        }
        const daysLeft = Math.ceil(moment(data)['diff'](moment(), 'hours') / 24);
        return `<button class="btn btn-primary">${moment(data).format('DD/MM/YYYY')}<br />(${daysLeft} ngày)</button>`;
    }
};

function makeColumn(title, name, render = null, disableSort = false) {
    const obj = {
        title: title,
        data: name,
        name: name
    };

    if (disableSort) {
        Object.assign(obj, { orderable: false, searchable: false });
    }

    if (!render) render = name;

    if (render) {
        if (typeof render === 'string') {
            if (typeof components[render] !== "undefined") {
                obj.render = components[render];
            } else if (typeof components.table[render] !== "undefined") {
                obj.render = components.table[render];
            } else if (!obj.render || obj.render === 'random') {
                const values = ['text_success', 'text_primary', 'text_warning', 'text_danger', 'text_info'];
                const randomIndex = Math.floor(Math.random() * values.length);
                obj.render = components.table[values[randomIndex]];
            }
        } else {
            obj.render = render;
        }
    }

    return obj;
}
function readJson(rawData) {
    try {
        if (rawData instanceof Object) return rawData;
        return JSON.parse(rawData) || {};
    } catch (e) {
        return {};
    }
}

function callAjaxPost(url, postData) {
    return new Promise(function (resolve) {
        $.ajax({
            type: "POST",
            url: url,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: postData,
            success: function (data) {
                return resolve(data);
            }
        });
    })
}

function reloadTable() {
    if (datatableVip) datatableVip.ajax.reload(showToolTip, false);
    if (datatableLog) datatableLog.ajax.reload(showToolTip, false);
}
