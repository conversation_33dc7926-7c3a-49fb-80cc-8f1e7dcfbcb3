<?php

namespace App\Models;

use App\Enums\PackageModeEnum;
use App\Enums\PackageStatusEnum;
use App\Enums\UserLevelEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Package extends Model
{
    protected $table = 'packages';

    protected $fillable = [
        'name',
        'tenant_id',
        'api_provider_id',
        'service_id',
        'package_list_id',
        'category_id',
        'description',
        'mode',
        'rate',
        'member_price',
        'collaborator_price',
        'agency_price',
        'distributor_price',
        'min',
        'max',
        'refill',
        'refill_type',
        'cancel',
        'service_type',
        'drip_feed',
        'deny_duplicates',
        'note',
        'allow_reaction',
        'status',
        'visibility',
        'service_name'
    ];
    protected $casts = [
        'rate' => 'decimal:2',
        'member_price' => 'decimal:2',
        'collaborator_price' => 'decimal:2',
        'agency_price' => 'decimal:2',
        'distributor_price' => 'decimal:2',
        'refill' => 'boolean',
        'cancel' => 'boolean',
        'deny_duplicates' => 'boolean',
        'allow_reaction' => 'boolean',
        'visibility' => 'boolean',
        'status' => PackageStatusEnum::class,
        'mode' => PackageModeEnum::class
    ];

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class, 'api_provider_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }
    public function packageList(): BelongsTo
    {
        return $this->belongsTo(PackageList::class, 'package_list_id');
    }

    public function scopeVisible($query)
    {
        return $query->where('visibility', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }
    public function getPriceForRank(UserLevelEnum|string $rank): string
    {
        $rankValue = $rank instanceof UserLevelEnum ? $rank->value : $rank;

        return match ($rankValue) {
            'member' => number_format((int)$this->member_price,0,'',''),
            'collaborator' => number_format((int)$this->collaborator_price,0,'',''),
            'agent' => number_format((int)$this->agency_price,0,'',''),
            'distributor' => number_format((int)$this->distributor_price,0,'',''),
            default => number_format((int)$this->member_price,0,'',''),
        };
    }
}
