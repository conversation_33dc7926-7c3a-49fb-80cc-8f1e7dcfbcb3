<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SupperAdmin\VoucherRequest;
use App\Models\Tenant;
use App\Models\Voucher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class VoucherController extends Controller
{
    public function index(){
        \Assets::addScriptsDirectly('assets/js/supper-admin/datatable-voucher.min.js');
        return view('supper-admin.vouchers.index');
    }
    public function ajaxData()
    {
        $data = Voucher::query()
            ->with('tenant')
            ->select('id', 'code','value','tenant_id','type', 'start_date', 'end_date','is_active');

        return DataTables::of($data)
            ->addColumn('tenant', function ($row) {
                return $row->tenant ? $row->tenant->domain : 'N/A';
            })
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('code', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }

    public function create()
    {
        \Assets::addScriptsDirectly([
            'assets/libs/flatpickr/flatpickr.min.js',
        ])->addStylesDirectly('assets/libs/flatpickr/flatpickr.min.css');
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        return view('supper-admin.vouchers.create',[
            'tenants' => $tenants,
        ]);
    }

    /**
     * Store a newly created voucher in storage.
     */
    public function store(VoucherRequest $request)
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            Voucher::query()->create($validated);

            DB::commit();

            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('success_msg', 'Thêm mới voucher thành công.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()
                ->route('supper-admin.vouchers.create')
                ->with('error_msg', 'Có lỗi xảy ra: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show the form for editing the specified voucher.
     */
    public function edit(string|int $id)
    {
        \Assets::addScriptsDirectly([
            'assets/libs/flatpickr/flatpickr.min.js',
        ])->addStylesDirectly('assets/libs/flatpickr/flatpickr.min.css');

        $voucher = Voucher::query()->findOrFail($id);
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.vouchers.edit', [
            'dataEdit' => $voucher,
            'tenants' => $tenants,
        ]);
    }

    /**
     * Update the specified voucher in storage.
     */
    public function update(VoucherRequest $request, string|int $id)
    {
        $voucher = Voucher::query()->findOrFail($id);

        DB::beginTransaction();
        try {
            $validated = $request->validated();

            // Chuyển đổi định dạng ngày
            $validated = $this->convertDateFormat($validated);

            $voucher->update($validated);

            DB::commit();

            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('success_msg', 'Cập nhật voucher thành công.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()
                ->route('supper-admin.vouchers.edit', $id)
                ->with('error_msg', 'Có lỗi xảy ra: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified voucher from storage.
     */
    public function destroy(string|int $id)
    {
        try {
            $voucher = Voucher::query()->findOrFail($id);
            $voucher->delete();

            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('success_msg', 'Xóa voucher thành công.');
        } catch (\Exception $e) {
            return redirect()
                ->route('supper-admin.vouchers.index')
                ->with('error_msg', 'Có lỗi xảy ra khi xóa voucher: ' . $e->getMessage());
        }
    }

    /**
     * Convert date format from d/m/Y to Y-m-d
     */
    private function convertDateFormat(array $data): array
    {
        // Chuyển đổi start_date
        if (!empty($data['start_date'])) {
            $startDate = \DateTime::createFromFormat('d/m/Y', $data['start_date']);
            if ($startDate) {
                $data['start_date'] = $startDate->format('Y-m-d');
            } else {
                $data['start_date'] = null;
            }
        }

        // Chuyển đổi end_date
        if (!empty($data['end_date'])) {
            $endDate = \DateTime::createFromFormat('d/m/Y', $data['end_date']);
            if ($endDate) {
                $data['end_date'] = $endDate->format('Y-m-d');
            } else {
                $data['end_date'] = null;
            }
        }

        return $data;
    }
}
