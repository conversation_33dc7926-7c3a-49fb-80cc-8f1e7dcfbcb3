@extends('clients.layouts.app')

@section('title', 'Giao dịch')

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">Danh sách giao dịch</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="list-transaction" class="table table-bordered">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th width="30%">Nội dung</th>
                        <th>Số tiền</th>
                        <th>Thời gian</th>
                        <th>Trạng thái</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('#list-transaction').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('client.transactions.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'description', name: 'description'},
                    {data: 'amount', name: 'amount'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'status', name: 'status'},
                ],
            });
        });
    </script>
@endpush
