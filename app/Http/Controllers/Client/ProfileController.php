<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        return view('clients.profile.index',[
            'user'=> $user,
        ]);
    }
    public function update(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'avatar' => 'nullable|max:255',
            'phone' => 'nullable|string|min:8|max:11',
            'old_password' => 'nullable|string',
            'new_password' => 'nullable|string|min:8|regex:/[a-z]/|regex:/[A-Z]/|regex:/[0-9]/|regex:/[@$!%*#?&]/|not_regex:/' . $user->username . '/',
            'confirm_password' => 'same:new_password',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user->full_name = $request->input('full_name');
        $user->email = $request->input('email');
        $user->phone = $request->input('phone');
        $user->avatar = $request->input('avatar');

        if ($request->filled('old_password') || $request->filled('new_password')) {
            if (!Hash::check($request->input('old_password'), $user->password)) {
                return redirect()->back()->withErrors(['old_password' => 'Mật khẩu cũ không chính xác'])->withInput();
            }

            $user->password = Hash::make($request->input('new_password'));
        }

        $user->save();

        return redirect()->back()->with('success_msg', 'Cập nhật thông tin thành công!');
    }
}
