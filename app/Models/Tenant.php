<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tenant extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'domain', 'verified', 'status', 'owner_id', 'data', 'activated_at'
    ];

    protected $casts = [
        'verified' => 'boolean',
        'activated_at' => 'datetime',
        'status' => BaseStatusEnum::class
    ];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class,'tenant_users')
            ->withPivot('role');
    }

    public function settings(): Has<PERSON>any
    {
        return $this->hasMany(TenantSetting::class);
    }
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

}
