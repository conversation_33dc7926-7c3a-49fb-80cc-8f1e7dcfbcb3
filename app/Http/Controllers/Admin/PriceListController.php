<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;

class PriceListController extends Controller
{
    public function index()
    {
        $query = Category::query()
            ->with('packages', function ($query) {
                $query->visible()
                    ->orderBy('name');
            })
            ->orderBy('name')
            ->get();
        return view('admin.price-list.index',[
            'data' => $query
        ]);
    }
}
