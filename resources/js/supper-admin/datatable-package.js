"use strict";

import { definedColumns, xAjax, reloadTable , components} from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.stt,
    definedColumns.category,
    definedColumns.name,
    definedColumns.member_price,
    definedColumns.collaborator_price,
    definedColumns.agency_price,
    definedColumns.distributor_price,
    definedColumns.status,
    definedColumns.created_at,
    definedColumns.action((data, type, row) => {
      return components.btn_edit(row, '/supper-admin/packages') + components.btn_delete(row, '/supper-admin/packages');
    }),
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/supper-admin/packages/ajax/data`),
    order: [[ 0, "desc" ]],
    columns: columns
  });
});
