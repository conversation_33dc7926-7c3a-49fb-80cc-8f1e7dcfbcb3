"use strict";
import { definedColumns, xAjax, reloadTable, showToolTip } from "../table";
$(document).ready(function () {
  reloadTable.datatableLog = $('#datatable-report').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/ajax/reports/log`),
    order: [[0, "desc"]],
    columns: [
      definedColumns.stt,
      definedColumns.code,
      definedColumns.price,
      definedColumns.description,
      definedColumns.status,
      definedColumns.created_at,
    ]
  });
  const filter = {transaction: 'transaction'}
  $('.tab-filter-report li a').click(function () {
    var selectedFilter = $(this).data('filter');
    if (selectedFilter != filter.filter) {
      filter.filter = selectedFilter;

      var url = '/ajax/reports/log';
      url += `?type=${selectedFilter}`;
      reloadTable.datatableLog.ajax.url(url).load(showToolTip);
    }
  });
});
