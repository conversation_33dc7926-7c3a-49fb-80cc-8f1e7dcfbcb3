<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransactionBank extends Model
{
    protected $table = 'transaction_banks';

    protected $fillable = [
        'gateway',
        'transactionDate',
        'accountNumber',
        'transactionNumber',
        'content',
        'transferType',
        'description',
        'transferAmount',
        'referenceCode',
    ];

    protected $casts = [
        'transactionDate' => 'datetime',
        'transferAmount' => 'decimal:2',
    ];
    
}
