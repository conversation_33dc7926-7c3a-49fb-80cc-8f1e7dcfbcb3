<?php

namespace App\Http\Controllers\Client;

use App\Enums\PackageStatusEnum;
use App\Enums\UserLevelEnum;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Package;
use App\Models\Platform;
use App\Models\Note;

class ServiceController extends Controller
{
    public function index(string $platform, string $category)
    {
        \Assets::addScriptsDirectly(['assets/js/services.min.js','assets/js/preload.min.js', 'assets/js/clients/datatable-history.min.js']);

        $categoryModel = Category::query()
                ->where('key', $category)
                ->whereHas('platform', fn($q) => $q->where('key', $platform))
                ->with(['packages' => fn($q) => $q
                    ->visible()
                    ->with('packageList')
                    ->orderBy('id', 'asc')
                ])
                ->firstOrFail();

        $tenantId = session('current_tenant_id');
        $note = Note::query()
            ->where('tenant_id', $tenantId)
            ->where('category_id', $categoryModel->id)
            ->first();

        $viewPath = "clients.services.{$platform}.{$categoryModel->key}";

        if (!view()->exists($viewPath)) {
            $viewPath = "clients.services.default";
        }

        return view($viewPath, [
            'platform' => $platform,
            'categories' => $categoryModel,
            'category'=> $category,
            'packages' => $categoryModel->packages,
            'note_content' => $note?->content,
            'note_link_youtube' => $note?->link_youtube,
        ]);
    }

    public function loadService(string $platform, string $category)
    {
        $platformModel = Platform::query()->where('key', $platform)->firstOrFail();
        $categoryModel = Category::query()->where('key', $category)
            ->where('platform_id', $platformModel->id)
            ->firstOrFail();
        $tenantId = session('current_tenant_id');
        $note = Note::query()
            ->where('tenant_id', $tenantId)
            ->where('category_id', $categoryModel->id)
            ->first();
        $packages = Package::query()->where('category_id', $categoryModel->id)
            ->visible()
            ->get()
            ->map(function ($package) {
                return [
                    'id' => $package->id,
                    'allow_reaction' => $package->allow_reaction,
                    'name' => $package->name,
                    'price' => $package->getPriceForRank(auth()->user()->level->value),
                    'info' => $package->note,
                    'package_name' => $package->packageList->name,
                    'min' => $package->min,
                    'max' => $package->max,
                    'status' => $package->status,
                    'package_key' => $package->packageList->key
                ];
            })
            ->keyBy('package_key');

        $status = PackageStatusEnum::options();
        return response()->json(
            [
                'prices' => $packages,
                'status' => $status,
            ]
        );
    }

}
