<?php

namespace Database\Seeders;

use App\Enums\BaseStatusEnum;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SocialMediaMarketingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $platforms = [
            [
                'name' => 'Facebook',
                'description' => 'Mạng xã hội phổ biến để kết nối và chia sẻ nội dung.',
                'status' => 'published',
                'key' => 'facebook',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Instagram',
                'description' => 'Nền tảng chia sẻ hình ảnh và video.',
                'status' => 'published',
                'key' => 'instagram',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Threads',
                'description' => 'Ứng dụng trò chuyện liên kết với Instagram.',
                'status' => 'published',
                'key' => 'threads',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'TikTok',
                'description' => 'Mạng xã hội video ngắn rất phổ biến.',
                'status' => 'published',
                'key' => 'tiktok',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Twitter',
                'description' => 'Nền tảng chia sẻ thông tin nhanh chóng (nay là X).',
                'status' => 'published',
                'key' => 'twitter',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'YouTube',
                'description' => 'Nền tảng chia sẻ video lớn nhất thế giới.',
                'status' => 'published',
                'key' => 'youtube',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Google',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'google',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Shopee',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'shopee',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Spotify',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'spotify',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'LinkedIn',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'linkedin',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];
        DB::table('platforms')->insert($platforms);

        $packageList = [
            [
                'name' => 'Gói 1',
                'key' => 'package-1',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 2',
                'key' => 'package-2',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 3',
                'key' => 'package-3',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 4',
                'key' => 'package-4',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 5',
                'key' => 'package-5',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 6',
                'key' => 'package-6',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 7',
                'key' => 'package-7',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 8',
                'key' => 'package-8',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 9',
                'key' => 'package-9',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 10',
                'key' => 'package-10',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        DB::table('package_lists')->insert($packageList);
        $categories = [
            [
                'platform_id' => 1,
                'name' => 'Like bài viết Facebook',
                'key' => 'like-post',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Bình luận bài viết Facebook',
                'key' => 'comment-post',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Lượt thích bình luận bài viết Facebook',
                'key' => 'like-comment-post',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Chia sẻ bài viết Facebook',
                'key' => 'share-post',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Like/Follow fanpage Facebook',
                'key' => 'like-follow-fanpage',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Đánh giá fanpage Facebook',
                'key' => 'review-fanpage',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Theo dõi trang cá nhân Facebook',
                'key' => 'follow-profile',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Theo thành viên nhóm Facebook',
                'key' => 'member-group',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Mắt livestream Facebook',
                'key' => 'view-livestream',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'View video Facebook',
                'key' => 'view-video',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'View story Facebook',
                'key' => 'view-story',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        DB::table('categories')->insert($categories);
    }
}
