<?php

namespace App\Enums;

enum PostTypeEnum: string
{
    case POST = 'post';

    case POPUP = 'popup';

    public function label(): string
    {
        return match ($this) {
            self::POST => 'Bài viết',
            self::POPUP => 'Popup',
        };
    }

    public function toHtml(): string
    {
        return match ($this) {
            self::POST => '<span class="badge bg-primary">Bài viết</span>',
            self::POPUP => '<span class="badge bg-pink">Popup</span>',
        };
    }
}
