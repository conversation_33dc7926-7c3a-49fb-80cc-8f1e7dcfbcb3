<?php

namespace App\Enums;

enum PackageStatusEnum: string
{
    case ACTIVE = 'active';
    case SLOW = 'slow';
    case PAUSED = 'paused';
    case STOPPED = 'stopped';

    public function label(): string
    {
        return match ($this) {
            self::ACTIVE => 'Hoạt động',
            self::SLOW => 'Chậm',
            self::PAUSED => 'Bảo trì',
            self::STOPPED => 'Ngừng nhận đơn',
        };
    }

    public function toHtml(): string
    {
        return match ($this) {
            self::ACTIVE => '<span class="badge bg-success">Hoạt động</span>',
            self::SLOW => '<span class="badge bg-orange">Chậm</span>',
            self::PAUSED => '<span class="badge bg-danger">Bảo trì</span>',
            self::STOPPED => '<span class="badge bg-orange">Ngừng nhận đơn</span>',
        };
    }
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($status) => [$status->value => $status->label()])
            ->toArray();
    }
}
