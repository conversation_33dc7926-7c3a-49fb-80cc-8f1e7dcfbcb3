<?php

return [
    'offline' => env('ASSETS_OFFLINE', true),
    'enable_version' => env('ASSETS_ENABLE_VERSION', false),
    'version' => env('ASSETS_VERSION', time()),
    'scripts' => [
        'app',
        'layout'
    ],
    'styles' => [
        'app',
    ],
    'resources' => [
        'scripts' => [
            'app' => [
                'use_cdn' => false,
                'location' => 'footer',
                'src' => [
                    'local' => [
                        '/assets/libs/bootstrap/bootstrap.min.js',
                        '/assets/js/app.min.js',
                        '/assets/js/init.min.js',
                        'assets/libs/simplebar/simplebar.min.js',
                        '/assets/libs/toastr/toastr.min.js'
                    ],
                ],
            ],
            'layout' => [
                'use_cdn' => false,
                'location' => 'header',
                'src' => [
                    'local' => [
                        'assets/libs/jquery/jquery.min.js',
                        'assets/js/preload.min.js'
                    ],
                ],
            ],
        ],
        'styles' => [
            'app' => [
                'use_cdn' => false,
                'location' => 'header',
                'src' => [
                    'local' => [
                        '/assets/css/bootstrap.css',
                        '/assets/libs/toastr/toastr.min.css',
                        '/assets/css/icons.min.css',
                        '/assets/css/app.css',
                    ],
                ]
            ],
        ],
    ],
];
