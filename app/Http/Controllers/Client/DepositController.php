<?php

namespace App\Http\Controllers\Client;

use App\Enums\TypeNotificationEnum;
use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;

class DepositController extends Controller
{
    public function index(){
        return view('clients.deposit.index');
    }

    public function checkUpdate() {
        $user = Auth()->user();

        $types = [
            TypeNotificationEnum::DEPOSIT->value,
            TypeNotificationEnum::PLUS_MONEY->value,
            TypeNotificationEnum::MINUS_MONEY->value,
        ];

        $notifications = Notification::query()
            ->where('user_id', $user->id)
            ->where('is_read', false)
            ->whereIn('type', $types)
            ->orderByDesc('id')
            ->get()
            ->groupBy('type');

        $latest = collect();
        foreach ($types as $type) {
            if (isset($notifications[$type])) {
                $latest->put($type, $notifications[$type]->first());
            }
        }

        $idsToUpdate = $latest->pluck('id')->filter()->all();
        if (!empty($idsToUpdate)) {
            Notification::query()->whereIn('id', $idsToUpdate)->update(['is_read' => true]);
        }

        return response()->json([
            'payment'     => $latest[TypeNotificationEnum::DEPOSIT->value]?->amount ?? 0,
            'plusMoney'   => $latest[TypeNotificationEnum::PLUS_MONEY->value]?->amount ?? 0,
            'minusMoney'  => $latest[TypeNotificationEnum::MINUS_MONEY->value]?->amount ?? 0,
        ]);
    }

}
