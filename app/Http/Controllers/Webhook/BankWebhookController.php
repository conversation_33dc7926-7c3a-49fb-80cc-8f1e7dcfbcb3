<?php

namespace App\Http\Controllers\Webhook;

use App\Datas\BankWebhookData;
use App\Events\BankWebhookEvent;
use App\Http\Controllers\Controller;
use App\Models\TransactionBank;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
class BankWebhookController extends Controller
{
    /**
     * @return Response|ResponseFactory
     *
     * @throws BindingResolutionException
     */

    public function handleWebhookEndpoint(Request $request)
    {
        // Xác thực token
        $token = $this->bearerToken($request);

        throw_if(
            tenant_setting('acb_token') && $token !== tenant_setting('acb_token'),
            ValidationException::withMessages(['message' => ['Invalid Token']])
        );

        if ($request->has('transactions') && is_array($request->transactions)) {
            foreach ($request->transactions as $transaction) {
                $this->processTransaction($transaction);
            }
        } else {
            $this->processTransaction($request->all());
        }

        return response()->noContent();
    }

    private function processTransaction(array $transaction)
    {
        $transactionBank = new BankWebhookData(
            $transaction['id'] ?? 0,
            $transaction['gateway'] ?? '',
            $transaction['transactionDate'] ?? '',
            $transaction['accountNumber'] ?? '',
            $transaction['transactionNumber'] ?? '',
            $transaction['content'] ?? '',
            $transaction['transferType'] ?? '',
            $transaction['description'] ?? '',
            $transaction['transferAmount'] ?? 0,
            $transaction['referenceCode'] ?? ''
        );

        throw_if(
            TransactionBank::query()->whereId($transactionBank->id)->exists(),
            ValidationException::withMessages(['message' => ['transaction này đã thực hiện']])
        );

        $model = new TransactionBank();
        $model->id = $transactionBank->id;
        $model->gateway = $transactionBank->gateway;
        $model->transactionDate = $transactionBank->transactionDate;
        $model->accountNumber = $transactionBank->accountNumber;
        $model->transactionNumber = $transactionBank->transactionNumber;
        $model->content = $transactionBank->content;
        $model->transferType = $transactionBank->transferType;
        $model->description = $transactionBank->description;
        $model->transferAmount = $transactionBank->transferAmount;
        $model->referenceCode = $transactionBank->referenceCode;
        $model->save();

        $pattern = '/' . tenant_setting('transfer_content') . '(\d{12})';
        preg_match($pattern, $transactionBank->content, $matches);

        if (isset($matches[0])) {
            $info = Str::of($matches[0])->replaceFirst(tenant_setting('transfer_content'), '')->value();
            event(new BankWebhookEvent($info, $transactionBank));
        }
    }
    private function bearerToken(Request $request)
    {
        $header = $request->header('Authorization', '');

        foreach (['Bearer ', 'Apikey '] as $prefix) {
            $position = strrpos($header, $prefix);

            if ($position !== false) {
                $header = substr($header, $position + strlen($prefix));
                return str_contains($header, ',') ? (strstr($header, ',', true) ?: null) : $header;
            }
        }

        return null;
    }
}
