<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Http\Requests\TenantSettingRequest;
use App\Models\Tenant;
use App\Models\TenantSetting;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class TenantSettingController extends Controller
{
    /**
     * Show the form for editing tenant settings.
     */
    public function edit(Tenant $tenant): View
    {
        $settings = $tenant->settings->pluck('value', 'key')->toArray();

        return view('supper-admin.tenants.settings.edit', [
            'tenant' => $tenant,
            'settings' => $settings
        ]);
    }

    /**
     * Update tenant settings.
     */
    public function update(TenantSettingRequest $request, Tenant $tenant)
    {
        $settings = $request->validated()['settings'] ?? [];
        
        foreach ($settings as $key => $value) {
            $tenant->settings()->updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }
        
        // Clear settings cache
        clear_tenant_settings_cache($tenant->id);
        
        return redirect()
            ->route('supper-admin.tenants.settings.edit', $tenant)
            ->with('success', 'Cài đặt đã được cập nhật thành công.');
    }
}
