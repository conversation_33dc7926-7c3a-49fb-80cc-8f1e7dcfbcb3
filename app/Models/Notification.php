<?php

namespace App\Models;

use App\Enums\TypeNotificationEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'title',
        'content',
        'tenant_id',
        'user_id',
        'is_read',
        'type',
        'amount'
    ];

    protected $casts = [
        'type' => TypeNotificationEnum::class
    ];
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
