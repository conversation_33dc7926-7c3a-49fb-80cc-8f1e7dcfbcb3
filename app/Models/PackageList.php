<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PackageList extends Model
{
    protected $table = 'package_lists';

    protected $fillable = [
        'name',
        'key',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class
    ];

    public function packages(): HasMany
    {
        return $this->hasMany(Package::class, 'package_list_id', 'id');
    }
}
