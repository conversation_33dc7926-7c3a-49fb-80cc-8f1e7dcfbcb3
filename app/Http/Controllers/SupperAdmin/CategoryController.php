<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Platform;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/supper-admin/datatable-category.min.js']);
        return view('supper-admin.categories.index');
    }
    public function ajaxData()
    {
        $data = Category::query()
            ->select('id', 'name', 'platform_id','key', 'created_at', 'status');
        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%')
                        ->orWhere('key', 'like', '%' . request('keyword') . '%');
                }
            })
            ->addColumn('platform_name', function ($row) {
                return $row->platform->name;
            })
            ->make(true);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $platforms = Platform::query()->select('id', 'name', 'status')->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        return view('supper-admin.categories.create', [
            'platforms' => $platforms,
        ]);

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'platform_id' => 'nullable|exists:platforms,id',
            'description' => 'nullable|string',
            'key' => 'required|string|max:255|unique:categories,key',
            'status' => 'nullable|string|max:60|in:published,draft,pending',
        ]);

        DB::beginTransaction();
        try {
            if (empty($validated['key'])) {
                $validated['key'] = \Illuminate\Support\Str::slug($validated['name']);
            }

            $category = Category::create($validated);

            DB::commit();

            return redirect()
                ->route('supper-admin.categories.index')
                ->with([
                    'success' => 'Thêm mới thành công danh mục',
                    'new_category_id' => $category->id
                ]);

        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi thêm danh mục: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors([
                    'error' => 'Lỗi hệ thống khi thêm mới danh mục. Vui lòng thử lại!'
                ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string|int $id)
    {
        $category = Category::query()->findOrFail($id);
        $platforms = Platform::query()->select('id', 'name', 'status')->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.categories.edit', [
            'dataEdit' => $category,
            'platforms' => $platforms,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $category = Category::query()->findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'platform_id' => 'nullable|exists:platforms,id',
            'description' => 'nullable|string',
            'key' => 'required|string|max:255|unique:categories,key,' . $category->id,
            'status' => 'nullable|string|max:60|in:published,draft,pending',
        ]);

        DB::beginTransaction();
        try {

            $category->update($validated);
            DB::commit();

            return redirect()
                ->route('supper-admin.categories.index')
                ->with('success', 'Cập nhật danh mục thành công.');
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi cập nhật danh mục: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi cập nhật danh mục. Vui lòng thử lại!']);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $category = Category::query()->findOrFail($id);

        DB::beginTransaction();
        try {
            $category->delete();
            DB::commit();

            return redirect()
                ->route('supper-admin.categories.index')
                ->with('success', 'Xóa danh mục thành công.');
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi xóa danh mục: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withErrors(['error' => 'Lỗi hệ thống khi xóa danh mục. Vui lòng thử lại!']);
        }
    }
}
