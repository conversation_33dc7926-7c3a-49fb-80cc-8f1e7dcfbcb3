<?php

namespace App\Models;

use App\Enums\OrderStatusEnum;
use App\Enums\SourceStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Order extends Model
{
    protected $table = 'orders';
    protected $fillable = [
        'user_id',
        'category_id',
        'package_id',
        'tenant_id',
        'source_id',
        'source_name',
        'source_type',
        'source_cost',
        'source_resp',
        'source_place',
        'source_status',
        'order_code',
        'url',
        'uid',
        'count',
        'start_number',
        'success_count',
        'runs',
        'interval',
        'price_per',
        'total_payment',
        'currency_code',
        'note',
        'order_status',
        'order_actions',
        'extra_note',
        'extra_data',
        'message',
        'admin_note',
        'processed_at'
    ];

    protected $casts = [
        'source_resp' => 'array',
        'source_place' => 'boolean',
        'source_cost' => 'decimal:2',
        'price_per' => 'decimal:2',
        'total_payment' => 'decimal:2',
        'order_actions' => 'array',
        'extra_data' => 'array',
        'processed_at' => 'datetime',
        'source_status' => SourceStatusEnum::class,
        'order_status' =>  OrderStatusEnum::class,
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }


    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }


    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }
    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class, 'source_name', 'id');
    }
}
