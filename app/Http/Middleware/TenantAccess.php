<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TenantAccess
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle( $request, Closure $next): Response
    {
        $tenant = $request->tenant;
        $user = Auth::user();

        if (!$user || !$user->tenants->contains($tenant)) {
            Auth::logout();
            return redirect()->route('login')->with('error_msg', '<PERSON>ui lòng thử lại sau hoặc liên hệ quản trị viên');
        }
        return $next($request);

    }
}
