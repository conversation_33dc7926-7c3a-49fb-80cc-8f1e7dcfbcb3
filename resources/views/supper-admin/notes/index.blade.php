@extends('admin.layouts.app')

@section('title', '<PERSON>hi chú')

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            Danh sách ghi chú
        </h4>
        <div class="">
            <a href="{{ route('supper-admin.notes.create') }}" class="btn btn-success">
                <i class="ri-add-line"></i>
                Thêm mới
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatables">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tiêu đề</th>
                        <th>Danh mục</th>
                        <th>Trạng thái</th>
                        <th><PERSON><PERSON><PERSON> tạo</th>
                        <th>Website</th>
                        <th><PERSON><PERSON> t<PERSON></th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('supper-admin.notes.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'title', name: 'title'},
                    {data: 'category', name: 'category'},
                    {data: 'status', name: 'status'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'tenant', name: 'tenant'},
                    {data: 'action', name:'action'}
                ],
                order: [[0, 'desc']],
            });
        });
    </script>
@endpush