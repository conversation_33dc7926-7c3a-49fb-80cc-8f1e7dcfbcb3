<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Model;

class Provider extends Model
{
    protected $table = 'providers';

    protected $fillable = [
        'name',
        'key',
        'url',
        'currency',
        'type',
        'balance',
        'exchange_rate',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class
    ];
}
