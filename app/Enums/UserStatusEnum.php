<?php

namespace App\Enums;
enum UserStatusEnum: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case LOCKED = 'locked';

    public function label(): string
    {
        return match ($this) {
            self::ACTIVE => 'Hoạt động',
            self::INACTIVE => 'Ngưng hoạt động',
            self::LOCKED => 'Khóa',
        };
    }
    public function toHtml(): string
    {
        return match ($this) {
            self::ACTIVE => '<span class="badge bg-success">Hoạt động</span>',
            self::INACTIVE => '<span class="badge bg-secondary">Ngưng hoạt động</span>',
            self::LOCKED => '<span class="badge bg-danger">Khóa</span>',
        };
    }

}

