@extends('admin.layouts.app')

@section('title', 'Giao dịch')

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">Danh sách giao dịch</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="transaction" class="table table-bordered">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Thành viên</th>
                        <th>Số tiền</th>
                        <th width="30%">Nội dung</th>
                        <th>Thời gian</th>
                        <th>Trạng thái</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('#transaction').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('admin.transactions.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'username', name: 'user.username'},
                    {data: 'amount', name: 'amount'},
                    {data: 'description', name: 'description'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'status', name: 'status'},
                ],
            });
        });
    </script>
@endpush
