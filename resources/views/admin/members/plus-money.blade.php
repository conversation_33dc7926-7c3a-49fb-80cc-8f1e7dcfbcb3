@extends('admin.layouts.app')

@section('title', 'Cộng tiền cho thành viên')

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/libs/select2/css/select2.min.css') }}">
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Cộng tiền thành viên</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.members.plus-money.post') }}" method="POST" class="form-action">
                @csrf
                <div class="form-group">
                    <label for="user_id" class="form-label required">
                        Thông tin thành viên
                    </label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">-- Ch<PERSON><PERSON> thành viên --</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->username }} ({{ $user->email }})</option>
                        @endforeach
                    </select>
                    @error('user_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
                <div class="form-group">
                    <label for="amount" class="form-label required">Số tiền</label>
                    <input type="text" class="form-control" id="amount" name="amount"
                           value="{{ old('amount') }}">
                    @error('amount')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
                <div class="form-group">
                    <label for="description" class="form-label required">Lý do thực hiện</label>
                    <input type="text" class="form-control" id="description" name="description"
                           value="{{ old('description') }}">
                    @error('description')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
                <button class="btn btn-success btn-submit" type="submit">
                    <i class="ri-add-line"></i>
                    Cộng tiền
                </button>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">Danh sách giao dịch cộng tiền</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="depositTransactionsTable" class="table table-bordered table-hover">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Thành viên</th>
                        <th>Số tiền</th>
                        <th width="30%">Nội dung</th>
                        <th>Thời gian</th>
                        <th>Trạng thái</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function () {
            $('#user_id').select2({
                placeholder: '-- Chọn thành viên --',
                allowClear: true,
                width: '100%'
            });
        });
    </script>

    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('#depositTransactionsTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('admin.members.plus-money') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'username', name: 'user.username'},
                    {data: 'amount', name: 'amount'},
                    {data: 'description', name: 'description'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'status', name: 'status'},
                ],
            });
        });
    </script>

@endpush
