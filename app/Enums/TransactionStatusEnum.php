<?php

namespace App\Enums;

enum TransactionStatusEnum: string
{
    case PENDING = 'pending';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case REFUNDED = 'refunded';
    case CANCELLED = 'cancelled';
    case PROCESSING = 'processing';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Đang chờ',
            self::COMPLETED => 'Hoàn thành',
            self::FAILED => 'Thất bại',
            self::REFUNDED => 'Đã hoàn tiền',
            self::CANCELLED => 'Đã hủy',
            self::PROCESSING => 'Đang xử lý',
        };
    }

    public function toHtml(): string
    {
        return match ($this) {
            self::PENDING => '<span class="badge bg-pink">Đang chờ</span>',
            self::COMPLETED => '<span class="badge bg-success"><PERSON><PERSON><PERSON> thành</span>',
            self::FAILED => '<span class="badge bg-danger">Thất bại</span>',
            self::REFUNDED => '<span class="badge bg-info">Đ<PERSON> hoàn tiền</span>',
            self::CANCELLED => '<span class="badge bg-secondary">Đã hủy</span>',
            self::PROCESSING => '<span class="badge bg-primary">Đang xử lý</span>',
        };
    }
}
