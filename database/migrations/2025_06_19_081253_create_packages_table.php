<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('tenant_id')->default(1)->constrained('tenants')->onDelete('cascade');
            $table->foreignId('api_provider_id')->constrained('providers')->onDelete('cascade');
            $table->string('service_id')->nullable();
            $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
            $table->foreignId('package_list_id')->constrained('package_lists')->onDelete('cascade');
            $table->text('description')->nullable();
            $table->string('mode', 60)->default('api');
            $table->decimal('rate', 15, 2)->nullable()->comment('Giá gốc từ API');
            $table->decimal('member_price', 15, 2)->default(0)->comment('Giá bán cho thành viên');
            $table->decimal('collaborator_price', 15, 2)->default(0)->comment('Giá bán cho cộng tác viên');
            $table->decimal('agency_price', 15, 2)->default(0)->comment('Giá bán cho đại lý');
            $table->decimal('distributor_price', 15, 2)->default(0)->comment('Giá bán cho nhà phân phối');
            $table->integer('min')->nullable();
            $table->integer('max')->nullable();
            $table->boolean('refill')->default(false);
            $table->string('refill_type')->default('manual');
            $table->boolean('cancel')->default(false);
            $table->string('service_type')->default('manual');
            $table->integer('drip_feed')->default(0);
            $table->boolean('deny_duplicates')->default(true);
            $table->text('note')->nullable();
            $table->boolean('allow_reaction')->default(false);
            $table->string('status', 60)->default('active');
            $table->boolean('visibility')->default(true);
            $table->timestamps();

            $table->index(['status', 'visibility', 'service_type']);
            $table->index(['category_id', 'status']);
            $table->index('service_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
