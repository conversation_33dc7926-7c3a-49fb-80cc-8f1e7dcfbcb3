<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\HandleMoneyMemberController;
use App\Http\Controllers\Admin\MemberController;
use App\Http\Controllers\Admin\PriceListController;
use App\Http\Controllers\Admin\TransactionController;
use Illuminate\Support\Facades\Route;

Route::prefix('admin')
    ->as('admin.')
    ->middleware(['access.tenant','identify.tenant', 'admin'])->group(function () {
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

        Route::prefix('members')->as('members.')->group(function () {
            Route::get('/', [MemberController::class, 'index'])->name('index');
            Route::get('/create', [MemberController::class, 'create'])->name('create');
            Route::post('/store', [MemberController::class, 'store'])->name('store');
            Route::get('/{member}', [MemberController::class, 'show'])->name('show');
            Route::get('/{member}/edit', [MemberController::class, 'edit'])->name('edit');
            Route::put('/{member}/update', [MemberController::class, 'update'])->name('update');
            Route::delete('/{member}/delete', [MemberController::class, 'destroy'])->name('destroy');

            Route::get('{user}/details', [MemberController::class, 'showDetail'])
                ->name('admin.members.detail');
        });
        Route::get('/plus-money', [HandleMoneyMemberController::class, 'showFormPlusMoney'])
            ->name('members.plus-money');
        Route::post('plus-money', [HandleMoneyMemberController::class, 'plushMoney'])->name('members.plus-money.post');
        Route::get('minus-money', [HandleMoneyMemberController::class, 'showFormMinusMoney'])
            ->name('members.minus-money');
        Route::post('minus-money', [HandleMoneyMemberController::class, 'minusMoney'])->name('members.minus-money.post');
        Route::prefix('transactions')->as('transactions.')->group(function () {
            Route::get('/', [TransactionController::class, 'index'])->name('index');


        });

        Route::get('price-list', [PriceListController::class, 'index'])->name('price-list.index');

    });
