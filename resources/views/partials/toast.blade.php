<script type="text/javascript">
    var BaseVariables = BaseVariables || {};
    BaseVariables.languages = {
        notices_msg: {{ Js::from(trans('notices')) }},
    };
</script>
{{--    @if (Session::has('success') || Session::has('error') || (isset($errors) && $errors->any()) || isset($error))--}}
{{--    <script type="text/javascript">--}}
{{--        $(function () {--}}
{{--            @if (Session::has('success'))--}}
{{--            toastr.success("{!! e(session('success')) !!}");--}}
{{--            @endif--}}

{{--            @if (Session::has('error'))--}}
{{--            toastr.error("{!! e(session('error')) !!}");--}}
{{--            @endif--}}

{{--            @if (isset($error))--}}
{{--            toastr.error("{!! e($error) !!}");--}}
{{--            @endif--}}

{{--            @if (isset($errors) && $errors->any())--}}
{{--            @foreach ($errors->all() as $error)--}}
{{--            toastr.error("{!! e($error) !!}");--}}
{{--            @endforeach--}}
{{--            @endif--}}
{{--        });--}}
{{--    </script>--}}
{{--@endif--}}
@if (Session::has('success_msg') || Session::has('error_msg') || (isset($errors) && $errors->any()) || isset($error_msg))
    <script type="text/javascript">
        $(function() {
            @if (Session::has('success_msg'))
            Base.showSuccess('{!! session('success_msg') !!}');
            @endif
            @if (Session::has('error_msg'))
            Base.showError('{!! session('error_msg') !!}');
            @endif
            @if (isset($error_msg))
            Base.showError('{!! $error_msg !!}');
            @endif
            @if (isset($errors))
            @foreach ($errors->all() as $error)
            Base.showError('{!! $error !!}');
            @endforeach
            @endif
        })
    </script>
@endif
