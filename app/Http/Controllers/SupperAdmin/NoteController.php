<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Note;
use App\Models\Tenant;
use App\Models\Category;
use App\Enums\BaseStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class NoteController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $notes = Note::with(['tenant', 'category'])->select('*');

            return DataTables::of($notes)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.notes.edit', $row->id);
                    $deleteUrl = route('supper-admin.notes.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->editColumn('tenant', function ($row) {
                    return '<span class="badge bg-purple">'.$row->tenant->domain.'</span>' ?? 'N/A';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('category', function ($row) {
                    return '<span class="badge bg-pink">'.$row->category->name.'</span>' ?? 'N/A';
                })
                ->rawColumns(['action', 'status','tenant','category'])
                ->make(true);
        }

        return view('supper-admin.notes.index');
    }

    public function create()
    {
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        $categories = Category::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.notes.create', compact('tenants', 'categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|string',
            'link_youtube' => 'nullable|string',
            'status' => 'nullable|string|max:60|in:published,draft,pending',
        ]);

        DB::beginTransaction();
        try {
            Note::create($validated);
            DB::commit();

            return redirect()
                ->route('supper-admin.notes.index')
                ->with('success', 'Thêm mới ghi chú thành công');
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi thêm ghi chú: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi thêm mới ghi chú. Vui lòng thử lại!']);
        }
    }

    public function edit(string|int $id)
    {
        $note = Note::query()->findOrFail($id);
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        $categories = Category::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();

        return view('supper-admin.notes.edit', [
            'dataEdit' => $note,
            'tenants' => $tenants,
            'categories' => $categories,
        ]);
    }

    public function update(Request $request, string|int $id)
    {
        $note = Note::query()->findOrFail($id);

        $validated = $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|string',
            'link_youtube' => 'nullable|string',
            'status' => 'nullable|string|max:60|in:published,draft,pending',
        ]);

        DB::beginTransaction();
        try {
            $note->update($validated);
            DB::commit();

            return redirect()
                ->route('supper-admin.notes.index')
                ->with('success', 'Cập nhật ghi chú thành công');
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi cập nhật ghi chú: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi cập nhật ghi chú. Vui lòng thử lại!']);
        }
    }

    public function destroy(string|int $id)
    {
        $note = Note::query()->findOrFail($id);

        DB::beginTransaction();
        try {
            $note->delete();
            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Xóa ghi chú thành công',
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi xóa ghi chú: ' . $exception->getMessage());

            return response()->json([
                'error' => true,
                'message' => 'Lỗi hệ thống khi xóa ghi chú. Vui lòng thử lại!',
            ]);
        }
    }
}
