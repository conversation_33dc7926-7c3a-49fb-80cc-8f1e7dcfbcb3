@extends('admin.layouts.app')
@section('title', 'Chỉnh sủa đơn hàng')
@section('content')
    <div class="row">
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h4 class="card-title">
                        Thông tin đơn hàng:
                        <span class="text-plain">{{ $order->order_code }}</span>
                    </h4>
                    <span class="d-flex align-items-center gap-1">
                        {!! $order->order_status->toHtml() !!}
                    </span>
                </div>
                <table class="table table-vcenter card-table">
                    <tbody>
                    <tr>
                        <td class="text-start">
                            <a href=""
                               class="text-plain">{{ $order->package->packageList->name .':'.$order->package->name }}</a>
                            <div class="fs-12 fw-medium">{{ $order->notes }}</div>
                            <ul class="list-unstyled ms-1 small">
                                <li>
                                    <span class="bull">↳</span>
                                </li>
                            </ul>
                        </td>
                        <td>
                            <span class="fw-medium">{{ $order->count}}</span>
                        </td>
                        <td>
                            x
                        </td>
                        <td>
                            <span class="fw-medium">
                                {{ number_format($order->price_per) }}đ
                            </span>
                        </td>
                        <td>
                            <span class="fw-medium">
                                {{ number_format($order->total_payment) }}đ
                            </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <table class="table table-vcenter card-table table-borderless text-end ">
                                <tbody>
                                <tr>
                                    <td class="fw-medium">Số lượng</td>
                                    <td>
                                        <span class="text-plain">{{ $order->count }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Giá tiền</td>
                                    <td>
                                        <span class="fw-medium">{{ number_format($order->price_per) }}đ</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Tổng giá</td>
                                    <td>
                                        <span class="text-plain">{{ number_format($order->total_payment) }}đ</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Bắt đầu</td>
                                    <td>
                                        <span class="fw-medium">{{ $order->start_number ?? 0}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Còn lại</td>
                                    <td>
                                        <span class="fw-medium">{{ $order->runs ?? 0}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Thời gian</td>
                                    <td>
                                        <span class="fw-medium">{{ $order->interval ?? 0}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Ghi chú</td>
                                    <td>
                                        <span class="fw-medium">{{ $order->order_note ?? 'N/A'}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Trạng thái nguồn</td>
                                    <td>
                                        <span class="fw-medium">{!! $order->source_status->toHtml()!!}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Trạng thái đơn hàng</td>
                                    <td>
                                        <span class="fw-medium">{!! $order->order_status->toHtml()!!}</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <form action="">
                                <div class="mb-3">
                                    <textarea name=admin_note" id="admin_note" cols="3" class="form-control mb-1">
                                    {{ $order->admin_note }}
                                </textarea>
                                    <small>Ghi chú lại nội dung muốn gửi đến khách hàng</small>
                                </div>
                                <button class="btn btn-success">Lưu lại</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <div class="p-3 border-bottom d-flex justify-content-between align-items-center">
                        <p class="fs-10 fw-medium m-0">URL : <span class="text-plain">{{$order->url}}</span></p>
                    </div>
                    <div class="p-3 border-bottom d-flex justify-content-between align-items-center">
                        <p class="fs-10 fw-medium m-0">UID : <span class="text-plain">{{$order->uid}}</span></p>
                    </div>
                    <div class="p-3 border-bottom d-flex align-items-center">
                        <button type="button" class="btn btn-primary me-2">
                            Trạng thái nguồn
                        </button>
                        <button type="button" class="btn btn-primary me-2">
                            Trạng thái đơn hàng
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        Thông tin khách hàng
                    </h4>
                </div>
                <div class="card-body">
                    @php
                        $user = $order->user;
                    @endphp
                    <div class="mb-3">
                        <img src="{{ $order->user->avatar ?? asset('assets/images/user.png')  }}" alt="avatar" class="avatar-md">
                    </div>
                    @if ($user->id)
                        <p class="mb-1 text-plain">
                            {{ $user->orders()->count() }}
                            đơn hàng(s)
                        </p>
                    @endif
                    @if($user->full_name)
                        <p class="mb-2 fw-semibold">{{ $user->full_name }}</p>
                    @endif

                    <p class="mb-2 fw-semibold">{{ $user->username }}</p>

                    @if ($user->email)
                        <p class="mb-2">
                            <a href="mailto:{{ $user->email }}" class="fw-medium">
                                {{ $user->email }}
                            </a>
                        </p>
                    @endif


                    @if ($user->phone)
                        <p class="mb-1">
                            <a href="tel:{{ $user->phone }}" class="fw-medium">
                                {{ $user->phone }}
                            </a>
                        </p>
                    @endif

                    <p class="mb-1">Số dư: {{ number_format($user->balance) }}</p>
                    <p class="mb-1">Cấp bậc: {!! $user->level->toHtml() !!} </p>
                    <p class="mb-1">Trạng thái tài khoản: {!! $user->status->toHtml() !!}</p>
                </div>
            </div>
        </div>
    </div>
@endsection
