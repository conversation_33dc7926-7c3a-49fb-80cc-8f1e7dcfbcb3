<?php

namespace App\Enums;

enum TransactionTypeEnum: string
{
    case DEPOSIT = 'deposit';
    case PAYMENT = 'payment';

    case PLUS_MONEY = 'plus_money';
    case MINUS_MONEY = 'minus_money';

    public function label(): string
    {
        return match ($this) {
            self::DEPOSIT => 'Nạp tiền',
            self::PAYMENT => 'Thanh toán',
            self::PLUS_MONEY => 'Cộng tiền',
            self::MINUS_MONEY => 'Trừ tiền',
        };
    }

    public function toHtml(): string
    {
        return match ($this) {
            self::DEPOSIT => '<span class="badge bg-success">Nạ<PERSON> tiền</span>',
            self::PAYMENT => '<span class="badge bg-primary"><PERSON><PERSON> toán</span>',
            self::PLUS_MONEY => '<span class="badge bg-primary">Cộng tiền</span>',
            self::MINUS_MONEY => '<span class="badge bg-danger">Trừ tiền</span>',
        };
    }

}
