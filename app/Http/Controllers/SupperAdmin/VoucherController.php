<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\Voucher;
use Yajra\DataTables\Facades\DataTables;

class VoucherController extends Controller
{
    public function index(){
        \Assets::addScriptsDirectly('assets/js/supper-admin/datatable-voucher.min.js');
        return view('supper-admin.vouchers.index');
    }
    public function ajaxData()
    {
        $data = Voucher::query()->select('id', 'code','value','tenant_id','type', 'start_date', 'end_date','is_active');
        return DataTables::of($data)
            ->addColumn('tenant', function ($row) {
                return $row->tenant->domain;
            })
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('code', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }

    public function create()
    {
        \Assets::addScriptsDirectly([
            'assets/libs/flatpickr/flatpickr.min.js',
        ])->addStylesDirectly('assets/libs/flatpickr/flatpickr.min.css');
        $tenants = Tenant::query()->where('status', BaseStatusEnum::PUBLISHED->value)->get();
        return view('supper-admin.vouchers.create',[
            'tenants' => $tenants,
        ]);

    }
}
